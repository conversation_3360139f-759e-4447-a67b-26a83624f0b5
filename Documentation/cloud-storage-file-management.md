# Cloud Storage & File Management - N8N Workflows

## Overview
This document catalogs the **Cloud Storage & File Management** workflows from the n8n Community Workflows repository.

**Category:** Cloud Storage & File Management  
**Total Workflows:** 27  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Manual Awss3 Automate Triggered
**Filename:** `0049_Manual_Awss3_Automate_Triggered.json`  
**Description:** Manual workflow that connects Awstranscribe and Awss3 for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Awstranscribe,Awss3,  

---

### Emailsend Googledrive Send Triggered
**Filename:** `0113_Emailsend_GoogleDrive_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Emailsend and Google Drive for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Emailsend,Google Drive,  

---

### Emailreadimap Nextcloud Send
**Filename:** `0134_Emailreadimap_Nextcloud_Send.json`  
**Description:** Manual workflow that connects Email (IMAP) and Nextcloud for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Email (IMAP),Nextcloud,  

---

### Awss3 Wait Automate Triggered
**Filename:** `0149_Awss3_Wait_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Google Sheets, Awstranscribe, and Google Drive for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Google Sheets,Awstranscribe,Google Drive,Awss3,  

---

### Awss3 Googledrive Import Triggered
**Filename:** `0151_Awss3_GoogleDrive_Import_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Drive and Awss3 for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Google Drive,Awss3,  

---

### Create an Onfleet task when a file in Google Drive is updated
**Filename:** `0187_Onfleet_GoogleDrive_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Drive and Onfleet to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Google Drive,Onfleet,  

---

### Notion Googledrive Create Triggered
**Filename:** `0272_Notion_GoogleDrive_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Notion and Google Drive to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Notion,Google Drive,  

---

### Manual Googledrive Automate Triggered
**Filename:** `0328_Manual_GoogleDrive_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Textsplittertokensplitter, OpenAI, and Google Drive for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Textsplittertokensplitter,OpenAI,Google Drive,Documentdefaultdataloader,Chainsummarization,  

---

### Wait Dropbox Create Webhook
**Filename:** `0582_Wait_Dropbox_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Dropbox, Httprequest, and Server-Sent Events to create new records. Uses 20 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Dropbox,Httprequest,Server-Sent Events,Form Trigger,Executeworkflow,  

---

### Stopanderror Awss3 Automation Webhook
**Filename:** `0592_Stopanderror_Awss3_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Stripe for data processing. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Splitout,Stripe,Awss3,  

---

### Awss3 Compression Automate Triggered
**Filename:** `0593_Awss3_Compression_Automate_Triggered.json`  
**Description:** Manual workflow that connects Compression and Awss3 for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Compression,Awss3,  

---

### Googledrive Googlesheets Create Triggered
**Filename:** `0839_GoogleDrive_GoogleSheets_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, OpenAI, and Google Drive to create new records. Uses 13 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Instagram,OpenAI,Google Drive,Google Sheets,Facebook,  

---

### Googledrivetool Extractfromfile Import Triggered
**Filename:** `0875_Googledrivetool_Extractfromfile_Import_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Extractfromfile for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Google Drive,Extractfromfile,Toolworkflow,Googledrivetool,Mcp,Executeworkflow,  

---

### Workflow management
**Filename:** `0969_Dropbox_Manual_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Dropbox, and Airtable for data processing. Uses 19 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,Dropbox,Airtable,Movebinarydata,Httprequest,  

---

### Automated Image Metadata Tagging (Community Node)
**Filename:** `0978_Stickynote_GoogleDrive_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects OpenAI and Google Drive for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** OpenAI,Google Drive,  

---

### Manual Box Automate Triggered
**Filename:** `1027_Manual_Box_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Box for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Box,  

---

### Box Automate Triggered
**Filename:** `1031_Box_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Box for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Box,  

---

### Manual Dropbox Automation Webhook
**Filename:** `1078_Manual_Dropbox_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Dropbox for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Dropbox,  

---

### Upload a file and get a list of all the files in a bucket
**Filename:** `1088_Manual_S3_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and S3 for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,S3,  

---

### RAG Workflow For Company Documents stored in Google Drive
**Filename:** `1141_Stickynote_GoogleDrive_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Google Drive, and Agent for data processing. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Lmchatgooglegemini,Google Drive,Agent,Vectorstorepinecone,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Chat,Embeddingsgooglegemini,Memorybufferwindow,Toolvectorstore,  

---

### AI Agent - Cv Resume - Automated Screening , Sorting , Rating and Tracker System
**Filename:** `1287_Googledocs_Googledrivetool_Monitor_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, Google Drive, and Google Docs for data processing. Uses 20 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Googlesheetstool,Google Drive,Google Docs,Agent,Extractfromfile,Gmail,Googledrivetool,Lmchatgroq,  

---

### DigialOceanUpload
**Filename:** `1371_Form_S3_Import_Triggered.json`  
**Description:** Webhook-triggered automation that connects S3 and Form Trigger for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** S3,Form Trigger,  

---

### Manual Googledrive Automation Triggered
**Filename:** `1376_Manual_GoogleDrive_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Vectorstorepinecone for data processing. Uses 22 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Google Drive,Vectorstorepinecone,Outputparserstructured,Documentdefaultdataloader,Chainllm,Chat,Textsplitterrecursivecharactertextsplitter,  

---

### Wait Dropbox Automation Webhook
**Filename:** `1549_Wait_Dropbox_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Dropbox, Httprequest, and Server-Sent Events for data processing. Uses 20 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Dropbox,Httprequest,Server-Sent Events,Form Trigger,Executeworkflow,  

---

### RAG Workflow For Company Documents stored in Google Drive
**Filename:** `1626_Stickynote_GoogleDrive_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Google Drive, and Agent for data processing. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Lmchatgooglegemini,Google Drive,Agent,Vectorstorepinecone,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Chat,Embeddingsgooglegemini,Memorybufferwindow,Toolvectorstore,  

---

### Google Doc Summarizer to Google Sheets
**Filename:** `1673_GoogleDrive_GoogleSheets_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Google Docs for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Google Drive,Google Docs,Google Sheets,Toolwikipedia,Cal.com,  

---

### Fetch the Most Recent Document from Google Drive
**Filename:** `1806_GoogleDrive_GoogleSheets_Import_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Google Docs for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Google Drive,Google Docs,Google Sheets,Toolwikipedia,Cal.com,  

---


## Summary

**Total Cloud Storage & File Management workflows:** 27  
**Documentation generated:** 2025-07-27 14:32:06  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
