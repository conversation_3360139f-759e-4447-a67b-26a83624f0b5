# Social Media Management - N8N Workflows

## Overview
This document catalogs the **Social Media Management** workflows from the n8n Community Workflows repository.

**Category:** Social Media Management  
**Total Workflows:** 23  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### New tweets
**Filename:** `0005_Manual_Twitter_Create_Triggered.json`  
**Description:** Manual workflow that connects Airtable and Twitter/X for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Airtable,Twitter/X,  

---

### Manual Twitter Automate Triggered
**Filename:** `0059_Manual_Twitter_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Twitter/X for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Twitter/X,  

---

### TwitterWorkflow
**Filename:** `0356_Manual_Twitter_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Twitter/X and Rocket.Chat for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Twitter/X,Rocket.Chat,  

---

### Openai Twitter Create
**Filename:** `0785_Openai_Twitter_Create.json`  
**Description:** Manual workflow that orchestrates Twitter/X, Google Sheets, and OpenAI to create new records. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Twitter/X,Google Sheets,OpenAI,Form Trigger,  

---

### Linkedin Splitout Create Triggered
**Filename:** `0847_Linkedin_Splitout_Create_Triggered.json`  
**Description:** Manual workflow that orchestrates Splitout, Gmail, and OpenAI to create new records. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Splitout,Gmail,OpenAI,LinkedIn,  

---

### Manual Linkedin Automation Webhook
**Filename:** `1096_Manual_Linkedin_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and LinkedIn for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,LinkedIn,  

---

### Hacker News to Video Template - AlexK1919
**Filename:** `1121_Linkedin_Wait_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Hackernews, and Toolhttprequest for data processing. Uses 48 nodes and integrates with 15 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (48 nodes)  
**Integrations:** Splitinbatches,Hackernews,Toolhttprequest,Dropbox,OpenAI,Google Drive,Twitter/X,Instagram,Agent,LinkedIn,Outputparserstructured,Httprequest,OneDrive,Youtube,S3,  

---

### New WooCommerce Product to Twitter and Telegram
**Filename:** `1165_Twitter_Telegram_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Twitter/X, Telegram, and Woocommerce for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Twitter/X,Telegram,Woocommerce,  

---

### Manual Reddit Automate Triggered
**Filename:** `1197_Manual_Reddit_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Reddit for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Reddit,  

---

### Receive updates when a new activity gets created and tweet about it
**Filename:** `1211_Twitter_Strava_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Twitter/X and Strava to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Twitter/X,Strava,  

---

### Scrape Twitter for mentions of company
**Filename:** `1212_Twitter_Slack_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Twitter/X, Datetime, and Slack for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Twitter/X,Datetime,Slack,  

---

### Social Media AI Agent - Telegram
**Filename:** `1280_Linkedin_Telegram_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Twitter/X, and OpenAI for data processing. Uses 26 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Markdown,Twitter/X,OpenAI,Airtable,Telegram,LinkedIn,Httprequest,  

---

### Automate LinkedIn Posts with AI
**Filename:** `1330_Linkedin_Schedule_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Notion, and LinkedIn for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Notion,LinkedIn,Form Trigger,  

---

### ✨🩷Automated Social Media Content Publishing Factory + System Prompt Composition
**Filename:** `1342_Linkedin_Telegram_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and Google Drive for data processing. Uses 100 nodes and integrates with 18 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (100 nodes)  
**Integrations:** Instagram,Twitter/X,Google Drive,Toolserpapi,Google Docs,Lmchatopenai,Agent,Toolworkflow,LinkedIn,Gmail,Telegram,Httprequest,Extractfromfile,Facebookgraphapi,Chat,Executeworkflow,Memorybufferwindow,Facebook,  

---

### Hacker News to Video Template - AlexK1919
**Filename:** `1491_Linkedin_Wait_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Hackernews, and Toolhttprequest for data processing. Uses 48 nodes and integrates with 15 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (48 nodes)  
**Integrations:** Splitinbatches,Hackernews,Toolhttprequest,Dropbox,OpenAI,Google Drive,Twitter/X,Instagram,Agent,LinkedIn,Outputparserstructured,Httprequest,OneDrive,Youtube,S3,  

---

### AI Social Media Publisher from WordPress
**Filename:** `1709_Linkedin_Wordpress_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and Lmchatopenrouter for data processing. Uses 20 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Instagram,Twitter/X,Lmchatopenrouter,Google Sheets,LinkedIn,Outputparserstructured,Wordpress,Chainllm,Facebook,  

---

### Automatizacion X
**Filename:** `1744_Twittertool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Agent, and Twittertool for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** OpenAI,Agent,Twittertool,Chat,Memorybufferwindow,  

---

### Social Media AI Agent - Telegram
**Filename:** `1782_Linkedin_Telegram_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Twitter/X, and OpenAI for data processing. Uses 26 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Markdown,Twitter/X,OpenAI,Airtable,Telegram,LinkedIn,Httprequest,  

---

### ✨🩷Automated Social Media Content Publishing Factory + System Prompt Composition
**Filename:** `1807_Linkedin_Googledocs_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and Google Docs for data processing. Uses 56 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (56 nodes)  
**Integrations:** Instagram,Twitter/X,Google Docs,Lmchatopenai,Agent,Toolworkflow,LinkedIn,Gmail,Httprequest,Facebookgraphapi,Chat,Executeworkflow,Memorybufferwindow,Facebook,  

---

### Automate LinkedIn Posts with AI
**Filename:** `1922_Linkedin_Schedule_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Notion, and LinkedIn for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Notion,LinkedIn,Form Trigger,  

---

### Notion to Linkedin
**Filename:** `1939_Linkedin_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Notion, and LinkedIn for data processing. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Notion,LinkedIn,Form Trigger,  

---

### Training Feedback Automation
**Filename:** `1951_Linkedin_Webhook_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Airtable, Webhook, and LinkedIn for data processing. Uses 16 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Airtable,Webhook,LinkedIn,Emailsend,Form Trigger,Cal.com,  

---

### Linkedin Automation
**Filename:** `2024_Linkedin_Telegram_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Airtable, and Telegram for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Airtable,Telegram,LinkedIn,  

---


## Summary

**Total Social Media Management workflows:** 23  
**Documentation generated:** 2025-07-27 14:37:21  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
