# Data Processing & Analysis - N8N Workflows

## Overview
This document catalogs the **Data Processing & Analysis** workflows from the n8n Community Workflows repository.

**Category:** Data Processing & Analysis  
**Total Workflows:** 125  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### typeform feedback workflow
**Filename:** `0004_GoogleSheets_Typeform_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Typeform and Google Sheets for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Google Sheets,  

---

### Insert data into a new row for a table in Coda
**Filename:** `0014_Manual_Coda_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Coda for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Coda,  

---

### verify email
**Filename:** `0019_Manual_Uproc_Send_Triggered.json`  
**Description:** Manual workflow that connects Uproc and Functionitem for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Uproc,Functionitem,  

---

### location_by_ip
**Filename:** `0025_Manual_Uproc_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Uproc, Awsses, and Functionitem for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Uproc,Awsses,Functionitem,  

---

### Googlesheets Webhook Automate Webhook
**Filename:** `0035_GoogleSheets_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Webhook for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Google Sheets,Webhook,  

---

### Crypto Airtable Update Webhook
**Filename:** `0042_Crypto_Airtable_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Crypto, and Webhook to update existing data. Uses 26 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (26 nodes)  
**Integrations:** Airtable,Crypto,Webhook,  

---

### Get Company by Name
**Filename:** `0056_Manual_Uproc_Import_Triggered.json`  
**Description:** Manual workflow that connects Uproc and Functionitem for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Uproc,Functionitem,  

---

### Get DNS entries
**Filename:** `0063_Manual_Uproc_Import_Triggered.json`  
**Description:** Manual workflow that connects Uproc and Functionitem for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Uproc,Functionitem,  

---

### Verify phone numbers
**Filename:** `0067_Manual_Uproc_Automation_Triggered.json`  
**Description:** Manual workflow that connects Uproc and Functionitem for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Uproc,Functionitem,  

---

### Xml Respondtowebhook Automate Webhook
**Filename:** `0081_Xml_Respondtowebhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Xml and Webhook for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Xml,Webhook,  

---

### Googlesheets Interval Process Scheduled
**Filename:** `0082_GoogleSheets_Interval_Process_Scheduled.json`  
**Description:** Manual workflow that orchestrates Spreadsheetfile, Google Sheets, and Dropbox for data processing. Uses 4 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Spreadsheetfile,Google Sheets,Dropbox,Interval,  

---

### Track an event in Segment
**Filename:** `0098_Manual_Segment_Monitor_Triggered.json`  
**Description:** Manual workflow that integrates with Segment for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Segment,  

---

### Webhook Airtable Automate Webhook
**Filename:** `0099_Webhook_Airtable_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Webhook, and Redis for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Airtable,Webhook,Redis,  

---

### Netlify Airtable Automate Triggered
**Filename:** `0103_Netlify_Airtable_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Netlify for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Netlify,  

---

### Manual Googlesheets Automate Triggered
**Filename:** `0120_Manual_GoogleSheets_Automate_Triggered.json`  
**Description:** Manual workflow that orchestrates Dropcontact, Google Sheets, and Lemlist for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Dropcontact,Google Sheets,Lemlist,  

---

### Noop Googlesheets Create Webhook
**Filename:** `0172_Noop_GoogleSheets_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Google Sheets to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,Google Sheets,  

---

### Create, update and get records in Quick Base
**Filename:** `0189_Manual_Quickbase_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Quickbase to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Quickbase,  

---

### Create a table and insert data into it
**Filename:** `0204_Manual_Questdb_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Questdb to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Questdb,  

---

### Smart Factory Use Case
**Filename:** `0212_Noop_Cratedb_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Pagerduty, Cratedb, and Amqp for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Pagerduty,Cratedb,Amqp,  

---

### Insert and update data in Airtable
**Filename:** `0218_Manual_Airtable_Update_Triggered.json`  
**Description:** Manual workflow that integrates with Airtable to update existing data. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Airtable,  

---

### Googlesheets Readbinaryfile Automate
**Filename:** `0222_GoogleSheets_Readbinaryfile_Automate.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Google Sheets, and Movebinarydata for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Google Sheets,Movebinarydata,  

---

### Googlesheets Cron Create Scheduled
**Filename:** `0234_GoogleSheets_Cron_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Google Sheets and MySQL to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Google Sheets,MySQL,  

---

### Googlesheets Cron Automation Scheduled
**Filename:** `0235_GoogleSheets_Cron_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Google Sheets and MySQL for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Google Sheets,MySQL,  

---

### Manual Googlesheets Create Scheduled
**Filename:** `0236_Manual_GoogleSheets_Create_Scheduled.json`  
**Description:** Manual workflow that orchestrates Interval, Server-Sent Events, and Google Sheets to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Interval,Server-Sent Events,Google Sheets,  

---

### Googlesheets Spreadsheetfile Create Webhook
**Filename:** `0237_GoogleSheets_Spreadsheetfile_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Spreadsheetfile, Google Sheets, and Webhook to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Spreadsheetfile,Google Sheets,Webhook,  

---

### Googlesheets Respondtowebhook Automate Webhook
**Filename:** `0238_GoogleSheets_Respondtowebhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Webhook for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,Webhook,  

---

### Googlesheets Readbinaryfile Automate
**Filename:** `0256_GoogleSheets_Readbinaryfile_Automate.json`  
**Description:** Manual workflow that orchestrates Readbinaryfile, Google Sheets, and Movebinarydata for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Readbinaryfile,Google Sheets,Movebinarydata,  

---

### Manual Googlesheets Create Triggered
**Filename:** `0257_Manual_GoogleSheets_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Salesforce, Google Sheets, and Renamekeys to create new records. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Salesforce,Google Sheets,Renamekeys,Itemlists,  

---

### Microsoftexcel Manual Create Triggered
**Filename:** `0258_Microsoftexcel_Manual_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Salesforce, Renamekeys, and Microsoftexcel to create new records. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Salesforce,Renamekeys,Microsoftexcel,Itemlists,  

---

### Auto WordPress Blog Generator (GPT + Postgres + WP Media)
**Filename:** `0263_Postgres_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Agent, and OpenAI for data processing. Uses 46 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (46 nodes)  
**Integrations:** Httprequest,Agent,OpenAI,PostgreSQL,  

---

### Code Postgres Automate Triggered
**Filename:** `0307_Code_Postgres_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects PostgreSQL and Google Sheets for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** PostgreSQL,Google Sheets,  

---

### Googlesheets Discord Create Triggered
**Filename:** `0314_GoogleSheets_Discord_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Discord to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,Discord,  

---

### Google Sheet to Mailchimp
**Filename:** `0349_Manual_GoogleSheets_Automation_Scheduled.json`  
**Description:** Manual workflow that orchestrates Interval, Google Sheets, and Mailchimp for data processing. Uses 4 nodes.  
**Status:** Active  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Interval,Google Sheets,Mailchimp,  

---

### Store the output of a phantom in Airtable
**Filename:** `0369_Manual_Airtable_Automation_Triggered.json`  
**Description:** Manual workflow that connects Airtable and Phantombuster for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Phantombuster,  

---

### Redis Code Create Scheduled
**Filename:** `0387_Redis_Code_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Microsoftteams, and Redis to create new records. Uses 23 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (23 nodes)  
**Integrations:** Httprequest,Microsoftteams,Redis,  

---

### Postgrestool Stickynote Send Triggered
**Filename:** `0404_Postgrestool_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Agent, and PostgreSQL for data processing. Uses 7 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** OpenAI,Agent,PostgreSQL,Chat,Memorybufferwindow,  

---

### Postgres Filter Import Scheduled
**Filename:** `0460_Postgres_Filter_Import_Scheduled.json`  
**Description:** Scheduled automation that connects PostgreSQL and N8N for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** PostgreSQL,N8N,  

---

### Schedule Googlesheets Automation Scheduled
**Filename:** `0474_Schedule_GoogleSheets_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Twitter/X and Google Sheets for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Twitter/X,Google Sheets,  

---

### Googlesheets Webhook Automate Webhook
**Filename:** `0496_GoogleSheets_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Webhook for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Google Sheets,Webhook,  

---

### Redis Schedule Import Scheduled
**Filename:** `0497_Redis_Schedule_Import_Scheduled.json`  
**Description:** Scheduled automation that connects Executeworkflow and Redis for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (17 nodes)  
**Integrations:** Executeworkflow,Redis,  

---

### MongoDB Agent
**Filename:** `0511_Mongodbtool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates MongoDB, OpenAI, and Agent for data processing. Uses 8 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** MongoDB,OpenAI,Agent,Toolworkflow,Chat,Memorybufferwindow,  

---

### Wait Redis Create Triggered
**Filename:** `0542_Wait_Redis_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Memorymanager, OpenAI, and Agent to create new records. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Memorymanager,OpenAI,Agent,Twilio,Memorybufferwindow,Redis,  

---

### Form Googlesheets Create Triggered
**Filename:** `0633_Form_GoogleSheets_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Google Sheets, and Emailsend to create new records. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Crypto,Google Sheets,Emailsend,Form Trigger,  

---

### Googlesheets Webflow Create Triggered
**Filename:** `0635_GoogleSheets_Webflow_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Form Trigger to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Google Sheets,Form Trigger,  

---

### Splitout Redis Create Webhook
**Filename:** `0638_Splitout_Redis_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Memorymanager, and Webhook to create new records. Uses 40 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Crypto,Memorymanager,Webhook,Agent,Google Sheets,Html,Splitout,Form Trigger,Lmchatgroq,Memorybufferwindow,Redis,  

---

### Form Googlesheets Create Triggered
**Filename:** `0648_Form_GoogleSheets_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Google Sheets, Slack, and Form Trigger to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (12 nodes)  
**Integrations:** Google Sheets,Slack,Form Trigger,  

---

### Code Postgres Update Scheduled
**Filename:** `0655_Code_Postgres_Update_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Hubspot, and Google Sheets to update existing data. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Splitinbatches,Hubspot,Google Sheets,Httprequest,PostgreSQL,  

---

### Postgrestool Stickynote Send Triggered
**Filename:** `0656_Postgrestool_Stickynote_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Postgrestool, Agent, and OpenAI for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Postgrestool,Agent,OpenAI,Chat,  

---

### Postgres Webhook Create Webhook
**Filename:** `0666_Postgres_Webhook_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and PostgreSQL to create new records. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Webhook,PostgreSQL,Postgrestool,Cal.com,Supabase,  

---

### Manual Googlesheets Update Triggered
**Filename:** `0728_Manual_GoogleSheets_Update_Triggered.json`  
**Description:** Manual workflow that orchestrates Splitinbatches, OpenAI, and Google Sheets to update existing data. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Agent,Outputparserstructured,  

---

### Googlesheets Slack Send Triggered
**Filename:** `0736_GoogleSheets_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Google Sheets, and Emailsend for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Google Sheets,Emailsend,Slack,  

---

### Airtable Create Triggered
**Filename:** `0756_Airtable_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Airtable, and Outputparserstructured to create new records. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Lmchatgooglegemini,Airtable,Outputparserstructured,Chainllm,Chat,Outputparserautofixing,  

---

### Googlesheets Googledrive Automate Triggered
**Filename:** `0812_GoogleSheets_GoogleDrive_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Google Drive for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Google Sheets,Google Drive,  

---

### Googlesheets Gmail Send Triggered
**Filename:** `0814_GoogleSheets_Gmail_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Agent for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Google Sheets,Agent,Gmail,Outputparserstructured,Form Trigger,  

---

### Googlesheets Respondtowebhook Import Webhook
**Filename:** `0818_GoogleSheets_Respondtowebhook_Import_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Google Sheets for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Webhook,Google Sheets,  

---

### Postgres Data Ingestion
**Filename:** `0822_Cron_Postgres_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with PostgreSQL for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** PostgreSQL,  

---

### Googlesheets Gmail Create Triggered
**Filename:** `0837_GoogleSheets_Gmail_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Google Sheets, and Gmail to create new records. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,Google Sheets,Gmail,Google Drive,  

---

### Manual Googlesheets Update Triggered
**Filename:** `0838_Manual_GoogleSheets_Update_Triggered.json`  
**Description:** Manual workflow that orchestrates Cal.com, Google Sheets, and Form Trigger to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Cal.com,Google Sheets,Form Trigger,  

---

### Stickynote Postgrestool Create Triggered
**Filename:** `0873_Stickynote_Postgrestool_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Postgrestool, PostgreSQL, and Executeworkflow to create new records. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Postgrestool,PostgreSQL,Executeworkflow,Toolworkflow,  

---

### Splitout Redis Create Triggered
**Filename:** `0894_Splitout_Redis_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Agent to create new records. Uses 46 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (46 nodes)  
**Integrations:** OpenAI,Splitout,Agent,Toolworkflow,Mcpclienttool,N8N,Mcp,Chat,Executeworkflow,Memorybufferwindow,Redis,  

---

### Wait Redis Automate Triggered
**Filename:** `0903_Wait_Redis_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Chat, and Form Trigger for data processing. Uses 30 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** OpenAI,Chat,Form Trigger,Executeworkflow,Redis,  

---

### Manual Googlesheets Update Triggered
**Filename:** `0906_Manual_GoogleSheets_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenrouter, Google Sheets, and Reddit to update existing data. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatopenrouter,Google Sheets,Reddit,Chainllm,Chainsummarization,  

---

### Googlesheets Slack Send Triggered
**Filename:** `0927_GoogleSheets_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Google Sheets, and Emailsend for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Google Sheets,Emailsend,Slack,  

---

### XML Conversion
**Filename:** `0943_Manual_Xml_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Xml for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Xml,  

---

### Googlesheets Slack Send Triggered
**Filename:** `0950_GoogleSheets_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Typeform, Google Sheets, and Emailsend for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Typeform,Google Sheets,Emailsend,Slack,  

---

### Manual Postgres Automate Triggered
**Filename:** `0962_Manual_Postgres_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with PostgreSQL for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** PostgreSQL,  

---

### Save Telegram reply to journal spreadsheet
**Filename:** `0974_GoogleSheets_Telegram_Export_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Google Sheets, and Functionitem for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,Google Sheets,Functionitem,  

---

### Manual Mongodb Automate Triggered
**Filename:** `0982_Manual_Mongodb_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with MongoDB for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** MongoDB,  

---

### Manual Cockpit Automate Triggered
**Filename:** `0990_Manual_Cockpit_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Cockpit for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Cockpit,  

---

### CFP Selection 1
**Filename:** `1018_Typeform_Airtable_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Typeform for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Airtable,Typeform,  

---

### Manual Redis Automate Triggered
**Filename:** `1025_Manual_Redis_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Redis for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Redis,  

---

### Convert the JSON data received from the CocktailDB API in XML
**Filename:** `1029_Manual_Xml_Process_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Xml for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Xml,  

---

### Manual Microsoftexcel Automate Triggered
**Filename:** `1033_Manual_Microsoftexcel_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Microsoftexcel for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Microsoftexcel,  

---

### Manual Cratedb Automate Triggered
**Filename:** `1054_Manual_Cratedb_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Cratedb for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Cratedb,  

---

### Manual Mysql Automation Triggered
**Filename:** `1055_Manual_Mysql_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with MySQL for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** MySQL,  

---

### Manual Postgres Automate Triggered
**Filename:** `1056_Manual_Postgres_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with PostgreSQL for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** PostgreSQL,  

---

### Append, lookup, update, and read data from a Google Sheets spreadsheet
**Filename:** `1062_Manual_GoogleSheets_Update_Triggered.json`  
**Description:** Manual workflow that integrates with Google Sheets to update existing data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Google Sheets,  

---

### Manual Googlesheets Automate Triggered
**Filename:** `1073_Manual_GoogleSheets_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Google Sheets for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,  

---

### Googlesheets Cron Automate Scheduled
**Filename:** `1106_GoogleSheets_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that integrates with Google Sheets for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,  

---

### ETL pipeline
**Filename:** `1108_Postgres_Googlecloudnaturallanguage_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates MongoDB, Twitter/X, and PostgreSQL for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** MongoDB,Twitter/X,PostgreSQL,Googlecloudnaturallanguage,Slack,  

---

### Airtable Mindee Automate Webhook
**Filename:** `1120_Airtable_Mindee_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Webhook, and Mindee for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Webhook,Mindee,  

---

### Customer and Sales Support
**Filename:** `1133_Googlesheetstool_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Googlesheetstool, OpenAI, and Agent for data processing. Uses 7 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Googlesheetstool,OpenAI,Agent,Chat,Memorybufferwindow,  

---

### Daily Language Learning
**Filename:** `1138_Airtable_Vonage_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Airtable, Hackernews, and Vonage for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Airtable,Hackernews,Vonage,Lingvanex,  

---

### SHEETS RAG
**Filename:** `1144_Postgres_Code_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Google Drive, and Google Sheets for data processing. Uses 23 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Lmchatgooglegemini,Google Drive,Google Sheets,Manualchat,Toolworkflow,Agent,PostgreSQL,Form Trigger,Executeworkflow,  

---

### Moving metrics from Google Sheets to Orbit
**Filename:** `1153_GoogleSheets_Orbit_Automation.json`  
**Description:** Manual workflow that connects Google Sheets and Orbit for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Google Sheets,Orbit,  

---

### Qualify new leads in Google Sheets via OpenAI's GPT-4
**Filename:** `1177_Openai_GoogleSheets_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Google Sheets,OpenAI,  

---

### Extract expenses from emails and add to Google Sheet
**Filename:** `1188_GoogleSheets_Emailreadimap_Create.json`  
**Description:** Manual workflow that orchestrates Email (IMAP), Google Sheets, and Mindee for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Email (IMAP),Google Sheets,Mindee,  

---

### Manual Stackby Automate Triggered
**Filename:** `1203_Manual_Stackby_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Stackby for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Stackby,  

---

### Posthog Webhook Automate Webhook
**Filename:** `1217_Posthog_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Posthog and Webhook for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Posthog,Webhook,  

---

### Airtable Lemlist Automate
**Filename:** `1220_Airtable_Lemlist_Automate.json`  
**Description:** Manual workflow that connects Airtable and Lemlist for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Lemlist,  

---

### Execute an SQL query in Microsoft SQL
**Filename:** `1234_Manual_Microsoftsql_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Microsoftsql for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Microsoftsql,  

---

### Translate questions about e-mails into SQL queries and run them
**Filename:** `1245_Postgres_Extractfromfile_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, Agent, and Extractfromfile for data processing. Uses 26 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Converttofile,Agent,Extractfromfile,PostgreSQL,Chat,Form Trigger,Executeworkflow,Cal.com,Lmchatollama,  

---

### Postgres Webhook Automation Webhook
**Filename:** `1249_Postgres_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and PostgreSQL for data processing. Uses 19 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Webhook,PostgreSQL,Postgrestool,Cal.com,Supabase,  

---

### Postgrestool Stickynote Automation Triggered
**Filename:** `1251_Postgrestool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Postgrestool, Agent, and OpenAI for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Postgrestool,Agent,OpenAI,Chat,  

---

### AI Social Media Caption Creator
**Filename:** `1261_Airtabletool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Airtabletool, and Airtable for data processing. Uses 10 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Airtabletool,Airtable,Agent,Form Trigger,Memorybufferwindow,  

---

### modelo do chatbot
**Filename:** `1350_Mysqltool_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Mysqltool for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Mysqltool,PostgreSQL,Chat,  

---

### modelo do chatbot
**Filename:** `1372_Mysqltool_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Mysqltool for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Mysqltool,PostgreSQL,Chat,  

---

### Chat with Postgresql Database
**Filename:** `1377_Postgrestool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** OpenAI,Agent,Chat,Postgrestool,Memorybufferwindow,  

---

### Splitout Redis Automation Webhook
**Filename:** `1388_Splitout_Redis_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, Memorymanager, and Webhook for data processing. Uses 40 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Crypto,Memorymanager,Webhook,Agent,Google Sheets,Html,Splitout,Form Trigger,Lmchatgroq,Memorybufferwindow,Redis,  

---

### ETL pipeline
**Filename:** `1421_Postgres_Googlecloudnaturallanguage_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates MongoDB, Twitter/X, and PostgreSQL for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** MongoDB,Twitter/X,PostgreSQL,Googlecloudnaturallanguage,Slack,  

---

### Wait Redis Send Triggered
**Filename:** `1431_Wait_Redis_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Memorymanager, OpenAI, and Agent for data processing. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Memorymanager,OpenAI,Agent,Twilio,Memorybufferwindow,Redis,  

---

### Contact Form Text Classifier for eCommerce
**Filename:** `1537_Form_GoogleSheets_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Emailsend for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Google Sheets,Emailsend,Form Trigger,Textclassifier,  

---

### Contact Form Text Classifier for eCommerce
**Filename:** `1554_Form_GoogleSheets_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Emailsend for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Google Sheets,Emailsend,Form Trigger,Textclassifier,  

---

### MongoDB Agent
**Filename:** `1555_Mongodbtool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates MongoDB, OpenAI, and Agent for data processing. Uses 8 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** MongoDB,OpenAI,Agent,Toolworkflow,Chat,Memorybufferwindow,  

---

### Qualify new leads in Google Sheets via OpenAI's GPT-4
**Filename:** `1618_Openai_GoogleSheets_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Google Sheets,OpenAI,  

---

### AI agent: expense tracker in Google Sheets and n8n chat
**Filename:** `1661_GoogleSheets_Stickynote_Monitor_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Google Sheets, and Agent for data processing. Uses 10 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Google Sheets,Agent,Toolworkflow,Informationextractor,Chat,Executeworkflow,Memorybufferwindow,  

---

### AI Social Media Caption Creator
**Filename:** `1723_Airtabletool_Stickynote_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Airtabletool, and Airtable for data processing. Uses 10 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** OpenAI,Airtabletool,Airtable,Agent,Form Trigger,Memorybufferwindow,  

---

### List Builder
**Filename:** `1739_Manual_GoogleSheets_Create_Triggered.json`  
**Description:** Manual workflow that orchestrates Airtop, Google Sheets, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Airtop,Google Sheets,Form Trigger,  

---

### RAG & GenAI App With WordPress Content
**Filename:** `1752_Postgres_Wordpress_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Markdown, and Splitinbatches for data processing. Uses 53 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (53 nodes)  
**Integrations:** Textsplittertokensplitter,Markdown,Splitinbatches,OpenAI,Webhook,Vectorstoresupabase,Agent,PostgreSQL,Wordpress,Documentdefaultdataloader,Httprequest,Chat,Supabase,  

---

### Analyze Reddit Posts with AI to Identify Business Opportunities
**Filename:** `1766_Manual_GoogleSheets_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Agent for data processing. Uses 22 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Google Sheets,Agent,Gmail,Reddit,Sentimentanalysis,Chainsummarization,  

---

### Log errors and avoid sending too many emails
**Filename:** `1777_Error_Postgres_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Emailsend, PostgreSQL, and Executeworkflow for data processing. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Emailsend,PostgreSQL,Executeworkflow,Cal.com,Pushover,  

---

### Gmail to Vector Embeddings with PGVector and Ollama
**Filename:** `1783_Splitout_Postgres_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Splitout, and Embeddingsollama for data processing. Uses 20 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Splitinbatches,Splitout,Embeddingsollama,Gmail,Vectorstorepgvector,PostgreSQL,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,  

---

### Youtube Searcher
**Filename:** `1788_Postgres_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Youtube, Splitinbatches, and Httprequest for data processing. Uses 21 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** Youtube,Splitinbatches,Httprequest,PostgreSQL,Executeworkflow,  

---

### Add new incoming emails to a Google Sheets spreadsheet as a new row.
**Filename:** `1833_GoogleSheets_Gmail_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Google Sheets and Gmail for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,Gmail,  

---

### Chat with Postgresql Database
**Filename:** `1848_Postgrestool_Stickynote_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** OpenAI,Agent,Chat,Postgrestool,Memorybufferwindow,  

---

### WordPress Contact Form (CF7) Responses and Classification
**Filename:** `1860_GoogleSheets_Gmail_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Webhook, and Google Sheets for data processing. Uses 24 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Lmchatgooglegemini,Webhook,Google Sheets,Gmail,Outputparserstructured,Chainllm,Textclassifier,  

---

### Build an MCP server with Airtable
**Filename:** `1899_Stickynote_Airtabletool_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtabletool, and Airtable for data processing. Uses 13 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** OpenAI,Airtabletool,Airtable,Agent,Mcp,Chat,Memorybufferwindow,  

---

### LinkedIn Profile Discovery
**Filename:** `1912_Manual_GoogleSheets_Automation_Triggered.json`  
**Description:** Manual workflow that connects Airtop and Google Sheets for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Airtop,Google Sheets,  

---

### New Ticket Alerts to Teams
**Filename:** `1933_Redis_Code_Create_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Microsoft Teams, and Redis for notifications and alerts. Uses 8 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Microsoft Teams,Redis,  

---

### RAG & GenAI App With WordPress Content
**Filename:** `1942_Postgres_Wordpress_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, Markdown, and Splitinbatches for data processing. Uses 53 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (53 nodes)  
**Integrations:** Textsplittertokensplitter,Markdown,Splitinbatches,OpenAI,Webhook,Vectorstoresupabase,Agent,PostgreSQL,Wordpress,Documentdefaultdataloader,Httprequest,Chat,Supabase,  

---

### Sync New Files From Google Drive with Airtable
**Filename:** `1987_Stickynote_Airtable_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Google Drive to synchronize data. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Airtable,Google Drive,  

---

### Synchronize your Google Sheets with Postgres
**Filename:** `1998_Splitout_Postgres_Sync_Scheduled.json`  
**Description:** Scheduled automation that orchestrates PostgreSQL, Splitout, and Google Sheets to synchronize data. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** PostgreSQL,Splitout,Google Sheets,Comparedatasets,  

---

### Suspicious_login_detection
**Filename:** `2014_Postgres_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Webhook, Html, and PostgreSQL for data processing. Uses 43 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (43 nodes)  
**Integrations:** Webhook,Html,PostgreSQL,Httprequest,Form Trigger,Slack,  

---

### ICP Company Scoring
**Filename:** `2021_Manual_GoogleSheets_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Cal.com, Google Sheets, and Form Trigger for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Cal.com,Google Sheets,Form Trigger,  

---

### n8n-農產品
**Filename:** `2035_Manual_GoogleSheets_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Splitout, and Google Sheets for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Splitout,Google Sheets,  

---


## Summary

**Total Data Processing & Analysis workflows:** 125  
**Documentation generated:** 2025-07-27 14:35:44  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
