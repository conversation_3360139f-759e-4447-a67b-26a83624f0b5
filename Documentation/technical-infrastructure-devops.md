# Technical Infrastructure & DevOps - N8N Workflows

## Overview
This document catalogs the **Technical Infrastructure & DevOps** workflows from the n8n Community Workflows repository.

**Category:** Technical Infrastructure & DevOps  
**Total Workflows:** 50  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Manual Git Automate Triggered
**Filename:** `0052_Manual_Git_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Git for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Git,  

---

### Travisci Github Automate Triggered
**Filename:** `0060_Travisci_GitHub_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects GitHub and Travisci for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,  

---

### Noop Github Automate Triggered
**Filename:** `0061_Noop_GitHub_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and GitHub for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Telegram,GitHub,  

---

### Automate assigning GitHub issues
**Filename:** `0096_Noop_GitHub_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with GitHub for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** GitHub,  

---

### Noop Github Create Triggered
**Filename:** `0108_Noop_GitHub_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with GitHub to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** GitHub,  

---

### Github Cron Create Scheduled
**Filename:** `0135_GitHub_Cron_Create_Scheduled.json`  
**Description:** Scheduled automation that connects GitHub and GitLab to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** GitHub,GitLab,  

---

### Code Github Create Scheduled
**Filename:** `0182_Code_GitHub_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Splitinbatches, and Httprequest to create new records. Uses 26 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** GitHub,Splitinbatches,Httprequest,N8N,Executeworkflow,Slack,  

---

### Create, update, and get an incident on PagerDuty
**Filename:** `0195_Manual_Pagerduty_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Pagerduty to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Pagerduty,  

---

### Create, update and get a case in TheHive
**Filename:** `0198_Manual_Thehive_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Thehive to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Thehive,  

---

### Analyze a URL and get the job details using the Cortex node
**Filename:** `0202_Manual_Cortex_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Cortex for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Cortex,  

---

### Receive updates when an event occurs in TheHive
**Filename:** `0205_Thehive_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Thehive to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Thehive,  

---

### Github Stickynote Create Triggered
**Filename:** `0264_GitHub_Stickynote_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects GitHub and Notion to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** GitHub,Notion,  

---

### Github Stickynote Update Triggered
**Filename:** `0289_GitHub_Stickynote_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects GitHub and Homeassistant to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** GitHub,Homeassistant,  

---

### Receive messages from a queue via RabbitMQ and send an SMS
**Filename:** `0291_Noop_Rabbitmq_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Rabbitmq and Vonage for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Rabbitmq,Vonage,  

---

### [n8n] Advanced URL Parsing and Shortening Workflow - Switchy.io Integration
**Filename:** `0392_Stopanderror_GitHub_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Converttofile, and GitHub for data processing. Uses 56 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (56 nodes)  
**Integrations:** Splitinbatches,Converttofile,GitHub,Webhook,Html,Httprequest,Form Trigger,  

---

### Error Mondaycom Update Triggered
**Filename:** `0395_Error_Mondaycom_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Monday.com and Datetime to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Monday.com,Datetime,  

---

### Code Github Create Scheduled
**Filename:** `0516_Code_GitHub_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Splitinbatches, and Executecommand to create new records. Uses 24 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** GitHub,Splitinbatches,Executecommand,Httprequest,Form Trigger,Executeworkflow,  

---

### Error Code Update Scheduled
**Filename:** `0518_Error_Code_Update_Scheduled.json`  
**Description:** Scheduled automation that connects N8N and Gmail to update existing data. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** N8N,Gmail,  

---

### Error N8n Import Triggered
**Filename:** `0545_Error_N8N_Import_Triggered.json`  
**Description:** Webhook-triggered automation that connects N8N and Webhook for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** N8N,Webhook,  

---

### Gitlab Filter Create Scheduled
**Filename:** `0557_Gitlab_Filter_Create_Scheduled.json`  
**Description:** Scheduled automation that connects N8N and GitLab to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (16 nodes)  
**Integrations:** N8N,GitLab,  

---

### Gitlab Code Create Triggered
**Filename:** `0561_Gitlab_Code_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates N8N, Splitinbatches, and GitLab to create new records. Uses 21 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** N8N,Splitinbatches,GitLab,Extractfromfile,  

---

### Code Github Create Scheduled
**Filename:** `0667_Code_GitHub_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Splitinbatches, and Httprequest to create new records. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** GitHub,Splitinbatches,Httprequest,N8N,Executeworkflow,  

---

### Create a release and get all releases
**Filename:** `0703_Manual_Sentryio_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Sentryio to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Sentryio,  

---

### Code Github Create Scheduled
**Filename:** `0718_Code_GitHub_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Splitinbatches, and Httprequest to create new records. Uses 25 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** GitHub,Splitinbatches,Httprequest,N8N,Executeworkflow,  

---

### Github Aggregate Create Webhook
**Filename:** `0876_GitHub_Aggregate_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, GitHub, and Toolworkflow to create new records. Uses 19 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Executeworkflow,GitHub,Toolworkflow,Httprequest,  

---

### Error Alert and Summarizer
**Filename:** `0945_Error_Code_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Gmail for notifications and alerts. Uses 13 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** OpenAI,Agent,Gmail,Outputparserstructured,N8N,  

---

### Email
**Filename:** `0972_Cortex_Emailreadimap_Send.json`  
**Description:** Manual workflow that orchestrates Thehive, Email (IMAP), and Cortex for data processing. Uses 15 nodes.  
**Status:** Active  
**Trigger:** Manual  
**Complexity:** medium (15 nodes)  
**Integrations:** Thehive,Email (IMAP),Cortex,  

---

### Github Slack Create Triggered
**Filename:** `0973_GitHub_Slack_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects GitHub and Slack to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** GitHub,Slack,  

---

### Manual Awslambda Automate Triggered
**Filename:** `0985_Manual_Awslambda_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Awslambda for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Awslambda,  

---

### Receive messages for a MQTT queue
**Filename:** `0992_Mqtt_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Mqtt for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Mqtt,  

---

### Github Automate Triggered
**Filename:** `0997_GitHub_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with GitHub for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** GitHub,  

---

### Gitlab Automate Triggered
**Filename:** `0998_Gitlab_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with GitLab for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** GitLab,  

---

### Trigger a build using the TravisCI node
**Filename:** `1000_Manual_Travisci_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Travisci for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Travisci,  

---

### Manual Rundeck Automate Triggered
**Filename:** `1008_Manual_Rundeck_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Rundeck for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Rundeck,  

---

### new
**Filename:** `1066_Manual_GitHub_Create_Triggered.json`  
**Description:** Manual workflow that integrates with GitHub for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** GitHub,  

---

### Extranet Releases
**Filename:** `1068_GitHub_Slack_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects GitHub and Slack for data processing. Uses 2 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** GitHub,Slack,  

---

### Manual Ftp Automation Webhook
**Filename:** `1093_Manual_Ftp_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Ftp for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Ftp,  

---

### Restore your credentials from GitHub
**Filename:** `1147_Splitout_GitHub_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Splitout, and Extractfromfile for data processing. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** GitHub,Splitout,Extractfromfile,Httprequest,N8N,  

---

### Github Manual Create Scheduled
**Filename:** `1149_GitHub_Manual_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, GitHub, and Splitinbatches to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (16 nodes)  
**Integrations:** Httprequest,GitHub,Splitinbatches,  

---

### Get a pipeline in CircleCI
**Filename:** `1162_Manual_Circleci_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Circleci for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Circleci,  

---

### Error Mailgun Automate Triggered
**Filename:** `1179_Error_Mailgun_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Mailgun for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Mailgun,  

---

### Code Review workflow
**Filename:** `1292_Code_GitHub_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates GitHub, Googlesheetstool, and OpenAI for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** GitHub,Googlesheetstool,OpenAI,Agent,Httprequest,  

---

### Building RAG Chatbot for Movie Recommendations with Qdrant and Open AI
**Filename:** `1363_Splitout_GitHub_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, GitHub, and OpenAI for data processing. Uses 27 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Textsplittertokensplitter,GitHub,OpenAI,Splitout,Agent,Extractfromfile,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Chat,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Restore your workflows from GitHub
**Filename:** `1760_Splitout_GitHub_Automate_Webhook.json`  
**Description:** Manual workflow that orchestrates GitHub, Splitout, and Extractfromfile for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** GitHub,Splitout,Extractfromfile,Httprequest,N8N,  

---

### Qdrant Vector Database Embedding Pipeline
**Filename:** `1776_Manual_Ftp_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Ftp, Splitinbatches, and OpenAI for data processing. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Ftp,Splitinbatches,OpenAI,Documentdefaultdataloader,Vectorstoreqdrant,Textsplittercharactertextsplitter,  

---

### Building RAG Chatbot for Movie Recommendations with Qdrant and Open AI
**Filename:** `1798_Splitout_GitHub_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Textsplittertokensplitter, GitHub, and OpenAI for data processing. Uses 27 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Textsplittertokensplitter,GitHub,OpenAI,Splitout,Agent,Extractfromfile,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Chat,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### n8n Error Report to Line
**Filename:** `1849_Error_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that integrates with Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,  

---

### GitLab MR Auto-Review & Risk Assessment
**Filename:** `1895_Gitlab_Code_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates GitLab, Agent, and Anthropic for data processing. Uses 23 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** GitLab,Agent,Anthropic,Gmail,Outputparserstructured,Httprequest,Outputparserautofixing,  

---

### [OPS] Restore workflows from GitHub to n8n
**Filename:** `1988_GitHub_Manual_Automate_Triggered.json`  
**Description:** Manual workflow that connects GitHub and N8N for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (17 nodes)  
**Integrations:** GitHub,N8N,  

---

### CV Evaluation - Error Handling
**Filename:** `1991_Error_Code_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that connects Gmail and Html for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Gmail,Html,  

---


## Summary

**Total Technical Infrastructure & DevOps workflows:** 50  
**Documentation generated:** 2025-07-27 14:37:42  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
