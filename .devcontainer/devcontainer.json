{"name": "n8n Workflows - Claude <PERSON>", "build": {"dockerfile": "Dockerfile", "args": {"TZ": "${localEnv:TZ:America/Los_Angeles}"}}, "runArgs": ["--cap-add=NET_ADMIN", "--cap-add=NET_RAW"], "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "eamodio.gitlens", "ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "tamasfe.even-better-toml"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "python.defaultInterpreterPath": "/usr/local/bin/python3", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "[python]": {"editor.defaultFormatter": "ms-python.black-formatter"}, "terminal.integrated.defaultProfile.linux": "zsh", "terminal.integrated.profiles.linux": {"bash": {"path": "bash", "icon": "terminal-bash"}, "zsh": {"path": "zsh"}}}}}, "remoteUser": "node", "mounts": ["source=claude-code-bashhistory,target=/commandhistory,type=volume", "source=claude-code-config,target=/home/<USER>/.claude,type=volume"], "remoteEnv": {"NODE_OPTIONS": "--max-old-space-size=4096", "CLAUDE_CONFIG_DIR": "/home/<USER>/.claude", "POWERLEVEL9K_DISABLE_GITSTATUS": "true"}, "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=delegated", "workspaceFolder": "/workspace", "postCreateCommand": "sudo /usr/local/bin/init-firewall.sh"}