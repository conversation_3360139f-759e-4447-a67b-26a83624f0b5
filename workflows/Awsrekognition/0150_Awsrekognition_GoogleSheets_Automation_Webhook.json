{"nodes": [{"name": "AWS Rekognition", "type": "n8n-nodes-base.awsRekognition", "position": [680, 700], "parameters": {"type": "detectText", "binaryData": true, "additionalFields": {}}, "credentials": {"aws": {"id": "9", "name": "aws"}}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [500, 700], "parameters": {"url": "https://www.nicepng.com/png/detail/54-542069_motivational-quotes-png.png", "options": {}, "responseFormat": "file", "queryParametersUi": {"parameter": []}, "headerParametersUi": {"parameter": []}}, "typeVersion": 1}, {"name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "disabled": true, "position": [500, 860], "parameters": {"url": "https://www.googleapis.com/customsearch/v1?key=[YOUR_KEY]&cx=[YOUR_CX]&q=office&searchType=image", "options": {}, "queryParametersUi": {"parameter": []}, "headerParametersUi": {"parameter": []}}, "typeVersion": 1}, {"name": "Set1", "type": "n8n-nodes-base.set", "position": [860, 700], "parameters": {"values": {"number": [], "string": [{"name": "img_name", "value": "={{$node[\"HTTP Request\"].binary.data.fileName}}"}, {"name": "img_link", "value": "={{$node[\"HTTP Request\"].parameter[\"url\"]}}"}, {"name": "img_txt", "value": "={{$json[\"TextDetections\"][1][\"DetectedText\"]}} {{$json[\"TextDetections\"][2][\"DetectedText\"]}}{{$json[\"TextDetections\"][3][\"DetectedText\"]}} {{$json[\"TextDetections\"][4][\"DetectedText\"]}} {{$json[\"TextDetections\"][5][\"DetectedText\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Function1", "type": "n8n-nodes-base.function", "position": [1040, 700], "parameters": {"functionCode": "for (item of items) {\n  item.json.lowerText = $node[\"Set1\"].json[\"img_txt\"].toLowerCase();\n}\nconsole.log('Done!');\n\nreturn items;"}, "typeVersion": 1}, {"name": "Google Sheets1", "type": "n8n-nodes-base.googleSheets", "position": [1220, 700], "parameters": {"options": {}, "sheetId": "qwertz", "operation": "append", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "2", "name": "google_sheets_oauth"}}, "typeVersion": 1}], "connections": {"Set1": {"main": [[{"node": "Function1", "type": "main", "index": 0}]]}, "Function1": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "AWS Rekognition", "type": "main", "index": 0}]]}, "AWS Rekognition": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}}}