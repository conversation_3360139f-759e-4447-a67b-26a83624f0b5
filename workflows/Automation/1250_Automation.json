{"\"nodes\"": "[", "\"id\"": "\"vBLHyjEnMK9EaWwQ\",", "\"name\"": "\"Mark OpenAi \"", "\"type\"": "\"main\",", "\"position\"": "[", "\"parameters\"": "[", "\"options\"": "{},", "\"credentials\"": "{", "\"openAiApi\"": "{", "\"typeVersion\"": "4.2", "\"text\"": "\"={{ $('When chat message received').item.json.chatInput }}\",", "\"agent\"": "\"openAiFunctionsAgent\",", "\"maxIterations\"": "10,", "\"systemMessage\"": "\"You are Airtable assistant. \\nYou need to process user's requests and run relevant tools for that. \\n\\nPlan and execute in right order runs of tools to get data for user's request.\\n\\nFeel free to ask questions before do actions - especially if you noticed some inconcistency in user requests that might be error/misspelling. \\n\\nIMPORTANT Always check right table and base ids before doing queries.\\n\\nIMPORTANT Use Code function to do aggregation functions that requires math like - count, sum, average and etc. Aggegation function could be recognized by words like \\\"how many\\\",\\\"count\\\",\\\"what number\\\" and etc.\\nUse Code function to generate graph and images.\\n\\nIMPORTANT If search with filter failed - try to fetch records without filter\\n\\nIMPORTANT Ask yourself before answering - am I did everything is possible? Is the answer is right? Is the answer related to user request?\\n\\nIMPORTANT Always return in response name of Base and Table where records from. \"", "\"promptType\"": "\"define\"", "\"height\"": "346,", "\"content\"": "\"### Set up steps\\n\\n1. **Separate workflows**:\\n\\t- Create additional workflow and move there Workflow 2.\\n\\n2. **Replace credentials**:\\n\\t- Replace connections and credentials in all nodes.\\n\\n3. **Start chat**:\\n\\t- Ask questions and don't forget to mention required base name.\"", "\"sessionKey\"": "\"={{ $('When chat message received').item.json.sessionId }}\",", "\"sessionIdType\"": "\"customKey\"", "\"webhookId\"": "\"abf9ab75-eaca-4b91-b3ba-c0f83d3daba4\",", "\"assignments\"": "[", "\"value\"": "\"assistants=v2\"", "\"rules\"": "{", "\"values\"": "[", "\"outputKey\"": "\"code\",", "\"conditions\"": "[", "\"version\"": "2,", "\"leftValue\"": "\"={{ $('Execute Workflow Trigger').item.json.query.filter_desc }}\",", "\"caseSensitive\"": "true,", "\"typeValidation\"": "\"strict\"", "\"combinator\"": "\"and\",", "\"operator\"": "{", "\"operation\"": "\"notExists\",", "\"rightValue\"": "\"\"", "\"renameOutput\"": "true", "\"aggregate\"": "\"aggregateAllItemData\"", "\"mergeLists\"": "true", "\"fieldsToAggregate\"": "{", "\"fieldToAggregate\"": "\"records\"", "\"singleValue\"": "true", "\"includeOtherFields\"": "true", "\"width\"": "280,", "\"color\"": "7,", "\"fields\"": "{", "\"stringValue\"": "\"get_base_tables_schema\"", "\"schemaType\"": "\"manual\",", "\"workflowId\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"id\",", "\"cachedResultName\"": "\"Airtable Agent Tools\"", "\"description\"": "\"Fetches the schema of tables in a specific base by id.\\n\\nInput:\\nbase_id: appHwXgLVrBujox4J\\n\\nOutput:\\ntable 1: field 1 - type string, fields 2 - type number\",", "\"inputSchema\"": "\"{\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"base_id\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"ID of the base to retrieve the schema for. Format - appHwXgLVrBujox4J\\\"\\n }\\n },\\n \\\"required\\\": [\\\"base_id\\\"]\\n}\",", "\"specifyInputSchema\"": "true", "\"jsCode\"": "\"// Example: convert the incoming query to uppercase and return it\\n\\nreturn `https://api.mapbox.com/styles/v1/mapbox/streets-v12/static/${query.markers}/-96.9749,41.8219,3.31,0/800x500?before_layer=admin-0-boundary&access_token=<your_public_key>`;\",", "\"resource\"": "\"base\",", "\"airtableTokenApi\"": "{", "\"base\"": "{", "\"onError\"": "\"continueRegularOutput\",", "\"url\"": "\"https://api.openai.com/v1/threads\",", "\"method\"": "\"POST\",", "\"pagination\"": "{", "\"completeExpression\"": "\"={{ $response.body.offset==undefined}}\",", "\"paginationCompleteWhen\"": "\"other\"", "\"jsonBody\"": "\"={\\n \\\"model\\\": \\\"gpt-4o-mini\\\",\\n \\\"messages\\\": [\\n {\\n \\\"role\\\": \\\"system\\\",\\n \\\"content\\\": {{ JSON.stringify($('Set schema and prompt').item.json.prompt) }}\\n },\\n {\\n \\\"role\\\": \\\"user\\\",\\n \\\"content\\\": \\\"{{ $('Execute Workflow Trigger').item.json.query.filter_desc }}\\\"\\n }],\\n \\\"response_format\\\":{ \\\"type\\\": \\\"json_schema\\\", \\\"json_schema\\\": {{ $('Set schema and prompt').item.json.schema }}\\n\\n }\\n }\",", "\"sendBody\"": "true,", "\"specifyBody\"": "\"json\",", "\"authentication\"": "\"predefinedCredentialType\",", "\"nodeCredentialType\"": "\"openAiApi\"", "\"httpQueryAuth\"": "{", "\"contentType\"": "\"multipart-form-data\",", "\"bodyParameters\"": "{", "\"parameterType\"": "\"formBinaryData\",", "\"inputDataFieldName\"": "\"data\"", "\"sendHeaders\"": "true,", "\"headerParameters\"": "{", "\"pinData\"": "{},", "\"connections\"": "{", "\"If1\"": "{", "\"main\"": "[", "\"node\"": "\"Merge\",", "\"index\"": "1", "\"Merge\"": "{", "\"Switch\"": "{", "\"Aggregate\"": "{", "\"Get Bases\"": "{", "\"Aggregate1\"": "{", "\"Aggregate2\"": "{", "\"Search records\"": "{", "\"ai_tool\"": "[", "\"Get base schema\"": "{", "\"Create map image\"": "{", "\"Get list of bases\"": "{", "\"OpenAI Chat Model\"": "{", "\"ai_languageModel\"": "[", "\"Window Buffer Memory\"": "{", "\"ai_memory\"": "[", "\"OpenAI - Get messages\"": "{", "\"OpenAI - Send message\"": "{", "\"Set schema and prompt\"": "{", "\"Get Base/Tables schema\"": "{", "\"OpenAI - Create thread\"": "{", "\"OpenAI - Download File\"": "{", "\"OpenAI - Run assistant\"": "{", "\"Process data with code\"": "{", "\"Upload file to get link\"": "{", "\"Execute Workflow Trigger\"": "{", "\"Airtable - Search records\"": "{", "\"When chat message received\"": "{", "\"If filter description exists\"": "{", "\"OpenAI - Generate search filter\"": "{"}