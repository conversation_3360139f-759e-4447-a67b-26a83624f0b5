{"\"meta\"": "{", "\"instanceId\"": "\"e4f78845dfed9ddcfba1945ae00d12e9a7d76eab052afd19299228ce02349d86\"", "\"nodes\"": "[", "\"id\"": "\"2b4c1e91-c64b-43cb-aba2-c6f8f5a17c79\",", "\"name\"": "\"Webhook\",", "\"type\"": "\"main\",", "\"position\"": "[", "\"parameters\"": "{", "\"text\"": "\"=**System Prompt:**\\n\\nYou are an AI assistant designed to process new leads and generate appropriate responses. Your role includes analyzing lead notes, categorizing them, and generating an email from the system to inform the relevant contact about the inquiry. Do not send the email as if it is directly from the customer; instead, draft it as a notification from the system summarizing the inquiry.\\n\\n### **Process Flow**\\n\\n1. **Analyzing Lead Notes:**\\n - Extract key details such as the customer name, organization, contact information, and their specific request. \\n - Determine if the inquiry relates to products, services, or solutions offered by the company.\\n\\n2. **Finding the Appropriate Contact(s):**\\n - Search the contact database to find the responsible person(s) for the relevant product, service, or solution. \\n - If one person is responsible, provide their email. \\n - If multiple people are responsible, list all emails separated by commas.\\n\\n3. **Generating an Email Notification:**\\n - Draft a professional email as a notification from the system.\\n - Summarize the customer’s inquiry.\\n - Include all relevant details to assist the recipient in addressing the inquiry.\\n\\n4. **Handling Invalid Leads:**\\n - If the inquiry is unrelated to products, services, or solutions (e.g., job inquiries or general product inquiries), classify it as invalid and return: \\n `\\\"Invalid Lead - Not related to products, services, or solutions.\\\"`\\n\\n### **Output Requirements**\\n\\n1. **For Relevant Leads:**\\n - **Email Address(es):** Provide the appropriate email(s). \\n - **Email Message Body:** Generate an email notification from the system summarizing the inquiry.\\n\\n2. **For Invalid Leads:**\\n - Return: `\\\"Invalid Lead - Not related to products, services, or solutions.\\\"`\\n\\n\\n### **Email Template for Relevant Leads**\\n\\n**Email Address(es):** [Relevant Email IDs]\\n\\n**Email Message Body:**\\n\\n_Subject: New Inquiry from Customer Regarding [Product/Service/Solution]_ \\n\\nDear [Recipient(s)], \\n\\nWe have received a new inquiry from a customer through our system. Below are the details: \\n\\n**Customer Name:** [Customer Name] \\n**Organization:** [Organization Name] \\n**Contact Information:** [Contact Details] \\n\\n**Inquiry Summary:** \\n[Summarized description of the customer's request, e.g., “The customer is seeking to upgrade their restroom facilities with touchless soap dispensers and tissue holders installed behind mirrors. They have requested a site visit to assess the location and provide a proposal.”] \\n\\n**Action Required:** \\nPlease prioritize this inquiry and reach out to the customer promptly to address their requirements. \\n\\nThank you, \\n[Your System Name] \\n\\n\\n### **Example Output**\\n\\n**Input Lead Notes:**\\n*\\\"Dear Syncbricks, We are looking to Develop Workflow Automation Soluition for our company, can you let us know the details what do you offer in tems of this.\\\"*\\n\\n**Output:**\\n\\n- **Email Address(es):** <EMAIL>\\n\\n- **Email Message Body:** \\n\\n_Subject: Workflow Automation Platform Integration_ \\n\\nDear -Emploiyee Name (s) --, \\n\\nWe have received a new inquiry from a customer through our system. Below are the details: \\n\\n**Customer Name:** Amjid Ali \\n**Organization:** Syncbricks LLC\\n**Contact Information:** ********* \\n\\n**Inquiry Summary:** \\nThe customer is asking for workflow automation for their company \\n\\n**Action Required:** \\nPlease prioritize this inquiry and reach out to the customer promptly to address their requirements. \\n\\nThank you, \\nSyncbricks LLC\\n\\n---\\nHere are the Lead Details\\nLead Name : {{ $json.data.lead_name }}\\nCompany : {{ $json.data.company_name }}\\nSource : {{ $json.data.source }}\\nNotes : {{ $json.data.notes }}\\nCity : {{ $json.data.city }}\\nCountry : {{ $json.data.country }}\\nMobile : {{ $json.data.mobile_no }}\",", "\"options\"": "{},", "\"promptType\"": "\"define\"", "\"typeVersion\"": "2", "\"credentials\"": "{", "\"openAiApi\"": "{", "\"sheetName\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"id\",", "\"value\"": "\"=Telephone Directory\"", "\"cachedResultUrl\"": "\"\",", "\"cachedResultName\"": "\"\"", "\"documentId\"": "{", "\"googleSheetsOAuth2Api\"": "{", "\"assignments\"": "[", "\"conditions\"": "[", "\"version\"": "2,", "\"leftValue\"": "\"={{ $json.output }}\",", "\"caseSensitive\"": "true,", "\"typeValidation\"": "\"strict\"", "\"combinator\"": "\"and\",", "\"operator\"": "{", "\"operation\"": "\"get\",", "\"rightValue\"": "\"**Invalid Lead - Not related to products, services, or solutions.**\"", "\"subject\"": "\"={{ $('Fields for Outlook').item.json.subject }}\",", "\"bodyContent\"": "\"={{ $json.html }}\\n<a href=\\\"https://erpnext.syncbricks.com/app/lead/{{ $('Webhook').item.json.body.name }}\\\" target=\\\"_blank\\\" rel=\\\"noopener noreferrer\\\">Here is Lead {{ $('Source Website and Status Open').item.json.body.name }} </a>\\n\",", "\"toRecipients\"": "\"= {{ $('Fields for Outlook').item.json.email_addresses }}\",", "\"additionalFields\"": "{", "\"bodyContentType\"": "\"html\"", "\"microsoftOutlookOAuth2Api\"": "{", "\"jsCode\"": "\"// Input email body\\nconst emailBody = $json.email_body || '';\\n\\n// Function to convert plain text email body into HTML\\nfunction formatEmailBodyAsHtml(body) {\\n // Replace markdown-like sections with corresponding HTML\\n let htmlBody = body\\n .replace(/\\\\*\\\\*Customer Name:\\\\*\\\\* (.+)/, '<p><strong>Customer Name:</strong> $1</p>')\\n .replace(/\\\\*\\\\*Organization:\\\\*\\\\* (.+)/, '<p><strong>Organization:</strong> $1</p>')\\n .replace(/\\\\*\\\\*Contact Information:\\\\*\\\\* (.+)/, '<p><strong>Contact Information:</strong> $1</p>')\\n .replace(/\\\\*\\\\*Inquiry Summary:\\\\*\\\\*\\\\s*([\\\\s\\\\S]+?)(?=\\\\n\\\\n\\\\*\\\\*Action Required:)/, '<p><strong>Inquiry Summary:</strong> $1</p>')\\n .replace(/\\\\*\\\\*Action Required:\\\\*\\\\*\\\\s*([\\\\s\\\\S]+)/, '<p><strong>Action Required:</strong> $1</p>');\\n\\n // Wrap each paragraph in `<p>` tags for better readability\\n htmlBody = htmlBody\\n .replace(/Dear (.+?),/, '<p>Dear <strong>$1</strong>,</p>')\\n .replace(/Thank you,\\\\s+(.+)/, '<p>Thank you,<br><strong>$1</strong></p>');\\n\\n return htmlBody;\\n}\\n\\n// Convert the email body into HTML\\nconst formattedHtmlBody = formatEmailBodyAsHtml(emailBody);\\n\\n// Return the formatted HTML\\nreturn {\\n html: formattedHtmlBody\\n};\\n\"", "\"singleValue\"": "true", "\"documentURL\"": "\"you-must-provide-the-doc-id\"", "\"googleDocsOAuth2Api\"": "{", "\"color\"": "3,", "\"width\"": "302.58963031819115,", "\"height\"": "660,", "\"content\"": "\"### Prepare for Email\\nThis node will get approprate Fields for Email \\nEmail Addresses:\\nSubject : \\nEmail Body : \"", "\"url\"": "\"=https://erpnext.syncbricks.com/api/resource/Lead/{{ $('Source Website and Status Open').item.json.body.name }}\",", "\"authentication\"": "\"predefinedCredentialType\",", "\"nodeCredentialType\"": "\"erpNextApi\"", "\"erpNextApi\"": "{", "\"webhookId\"": "\"a39ea4e2-99b7-4ae1-baff-9fb370333e2a\",", "\"path\"": "\"new-lead-generated-in-erpnext\",", "\"httpMethod\"": "\"POST\"", "\"pinData\"": "{},", "\"connections\"": "{", "\"Webhook\"": "{", "\"main\"": "[", "\"node\"": "\"Email Body for Outlook\",", "\"index\"": "0", "\"Lead Body\"": "{", "\"Abbriviations\"": "{", "\"ai_tool\"": "[", "\"Company Profile\"": "{", "\"Company Policies\"": "{", "\"Inquiry has Notes\"": "{", "\"Inquiry is Valid?\"": "{", "\"OpenAI Chat Model\"": "{", "\"ai_languageModel\"": "[", "\"Fields for Outlook\"": "{", "\"Customer Lead AI Agent\"": "{", "\"Email Body for Outlook\"": "{", "\"Company Contact Database\"": "{", "\"Get Lead Data from ERPNext\"": "{", "\"Source Website and Status Open\"": "{", "\"Email Body Text Generated by AI\"": "{"}