{"\"meta\"": "{", "\"instanceId\"": "\"26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e\"", "\"nodes\"": "[", "\"id\"": "\"787bb405-1744-43b7-8c47-1a2c23331e05\",", "\"name\"": "\"Sticky Note8\",", "\"type\"": "\"main\",", "\"position\"": "[", "\"parameters\"": "{", "\"width\"": "181.85939799093455,", "\"height\"": "308.12010511833364,", "\"content\"": "\"\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n### 🚨Required!\\nRemember to set your Notion Database here.\"", "\"typeVersion\"": "1", "\"model\"": "\"gpt-4o\",", "\"options\"": "{", "\"temperature\"": "0", "\"credentials\"": "{", "\"openAiApi\"": "{", "\"url\"": "\"https://serpapi.com/search\",", "\"fields\"": "\"position,title,link,snippet,source\",", "\"method\"": "\"POST\",", "\"sendBody\"": "true,", "\"dataField\"": "\"organic_results\",", "\"authentication\"": "\"predefinedCredentialType\",", "\"parametersBody\"": "{", "\"values\"": "[", "\"value\"": "\"={{\\n {\\n ...$('Company Overview Agent').item.json.output,\\n ...$('Company Product Offering Agent').item.json.output,\\n ...$('Company Product Reviews Agent').item.json.output,\\n }\\n}}\"", "\"valueProvider\"": "\"fieldValue\"", "\"fieldsToInclude\"": "\"selected\",", "\"genericAuthType\"": "\"httpHeaderAuth\"", "\"toolDescription\"": "\"Call this tool to search for the latest news articles of a company.\",", "\"optimizeResponse\"": "true,", "\"httpHeaderAuth\"": "{", "\"jsonSchemaExample\"": "\"{\\n \\\"number_of_reviews\\\": 0,\\n \\\"positive_mentions_%\\\": \\\"\\\",\\n \\\"negative_mentions_%\\\": \\\"\\\",\\n \\\"top_pros\\\": [\\\"\\\"],\\n \\\"top_cons\\\": [\\\"\\\"],\\n \\\"top_countries\\\": [\\\"\\\"],\\n \\\"top_social_media_platforms\\\": [\\\"\\\"]\\n}\"", "\"compare\"": "\"selectedFields\",", "\"fieldsToCompare\"": "\"url\"", "\"assignments\"": "[", "\"fieldToSplitOut\"": "\"results\"", "\"sendQuery\"": "true,", "\"parametersQuery\"": "{", "\"nodeCredentialType\"": "\"serpApi\"", "\"serpApi\"": "{", "\"placeholderDefinitions\"": "{", "\"description\"": "\"the url or lik to the review site webpage.\"", "\"title\"": "\"={{ $json.output.company_name }}\",", "\"blockUi\"": "{", "\"blockValues\"": "[", "\"textContent\"": "\"={{ $json.output.top_cons.join(', ') }}\"", "\"resource\"": "\"databasePage\",", "\"databaseId\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"list\",", "\"cachedResultUrl\"": "\"https://www.notion.so/2d1c3c726e8e42f3aecec6338fd24333\",", "\"cachedResultName\"": "\"n8n Competitor Analysis\"", "\"propertiesUi\"": "{", "\"propertyValues\"": "[", "\"key\"": "\"Cons|rich_text\",", "\"notionApi\"": "{", "\"maxItems\"": "10", "\"bodyParameters\"": "{", "\"color\"": "7,", "\"webhookId\"": "\"94b5b09f-0599-4585-b83b-f669726bc2ef\",", "\"amount\"": "2", "\"text\"": "\"={{ $('Loop Over Items').item.json.url }}\",", "\"systemMessage\"": "\"Your role is customer reviews agent. Your goal is to gather and collect online customer reviews for a company or their product or service.\\n* number of reviews\\n* Positive mentions, %\\n* Negative mentions, %\\n* Top pros\\n* Top cons\\n* Top countries\\n* Top social media platforms\\n\\n## steps\\n1. search for review sites that may have reviews for the company or product in question. retrieve the links or urls of the serch results where the reviews are found.\\n2. Identify relevant items in the search result and and extract the urls from the search results.\\n2. using the extracted urls from the search results, fetch the webpage of the review sites containing reviews for the company or product.\\n3. extract the reviews from the fetched review sites to populate the required data points.\\n\\nIf a data point is not found after completing all the above steps, do not use null values in your final response. Use either an empty array, object or string depending on the required schema for the data point.\\nDo not retry any link that returns a 400,401,403 or 500 error code.\"", "\"promptType\"": "\"define\",", "\"hasOutputParser\"": "true", "\"pinData\"": "{},", "\"connections\"": "{", "\"2sec\"": "{", "\"main\"": "[", "\"node\"": "\"Set Source Company\",", "\"index\"": "0", "\"Limit\"": "{", "\"Extract Domain\"": "{", "\"Collect Results\"": "{", "\"Loop Over Items\"": "{", "\"Results to List\"": "{", "\"Search LinkedIn\"": "{", "\"ai_tool\"": "[", "\"Webscraper Tool\"": "{", "\"Get Company News\"": "{", "\"Search WellFound\"": "{", "\"Webscraper Tool1\"": "{", "\"Webscraper Tool2\"": "{", "\"OpenAI Chat Model\"": "{", "\"ai_languageModel\"": "[", "\"Remove Duplicates\"": "{", "\"Search Crunchbase\"": "{", "\"Insert Into Notion\"": "{", "\"OpenAI Chat Model1\"": "{", "\"OpenAI Chat Model2\"": "{", "\"Set Source Company\"": "{", "\"Company Overview Agent\"": "{", "\"Search Company Website\"": "{", "\"Structured Output Parser\"": "{", "\"ai_outputParser\"": "[", "\"Structured Output Parser1\"": "{", "\"Structured Output Parser2\"": "{", "\"Search Product Review Sites\"": "{", "\"Check Company Profiles Exist\"": "{", "\"Competitor Search via Exa.ai\"": "{", "\"Company Product Reviews Agent\"": "{", "\"Company Product Offering Agent\"": "{", "\"When clicking ‘Test workflow’\"": "{"}