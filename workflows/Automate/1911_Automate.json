{"\"meta\"": "{", "\"instanceId\"": "\"f0a68da631efd4ed052a324b63ff90f7a844426af0398a68338f44245d1dd9e5\"", "\"nodes\"": "[", "\"id\"": "\"67\",", "\"name\"": "\"Lucas Open AI\"", "\"type\"": "\"main\",", "\"position\"": "[", "\"parameters\"": "{", "\"email\"": "\"={{ $json[\\\"leadEmail\\\"] }}\",", "\"resource\"": "\"contact\",", "\"operation\"": "\"unsubscribe\",", "\"campaignId\"": "\"={{$json[\\\"campaignId\\\"]}}\"", "\"credentials\"": "{", "\"lemlistApi\"": "{", "\"typeVersion\"": "1", "\"metadata\"": "{", "\"subject\"": "\"=OOO - Follow up with {{ $json[\\\"properties\\\"][\\\"firstname\\\"][\\\"value\\\"] }} {{ $json[\\\"properties\\\"][\\\"lastname\\\"][\\\"value\\\"] }}\"", "\"authentication\"": "\"oAuth2\"", "\"additionalFields\"": "{", "\"associations\"": "{", "\"contactIds\"": "\"={{ $json[\\\"vid\\\"] }}\"", "\"hubspotOAuth2Api\"": "{", "\"rules\"": "[", "\"value2\"": "\"Out of Office\"", "\"output\"": "2,", "\"value1\"": "\"={{ $json[\\\"text\\\"].trim() }}\",", "\"dataType\"": "\"string\",", "\"fallbackOutput\"": "3", "\"mode\"": "\"combine\",", "\"options\"": "{", "\"clashHandling\"": "{", "\"values\"": "{", "\"resolveClash\"": "\"preferInput1\"", "\"combinationMode\"": "\"mergeByPosition\"", "\"url\"": "\"=https://api.lemlist.com/api/campaigns/YOUR_CAMPAIGN_ID/leads/{{$json[\\\"leadEmail\\\"]}}/interested\",", "\"requestMethod\"": "\"POST\",", "\"nodeCredentialType\"": "\"lemlistA<PERSON>\"", "\"stage\"": "\"79009480\",", "\"dealName\"": "\"=New Deal with {{ $json[\\\"identity-profiles\\\"][0][\\\"identities\\\"][0][\\\"value\\\"] }}\",", "\"associatedVids\"": "\"={{$json[\\\"canonical-vid\\\"]}}\"", "\"lastName\"": "\"={{ $json[\\\"leadLastName\\\"] }}\",", "\"firstName\"": "\"={{ $json[\\\"leadFirstName\\\"] }}\"", "\"text\"": "\"=Hello a lead replied to your emails. \\n\\nMore info in lemlist here: \\nhttps://app.lemlist.com/teams/{{$json[\\\"teamId\\\"]}}/reports/campaigns/{{$json[\\\"campaignId\\\"]}}\",", "\"channel\"": "\"Your channel name\",", "\"attachments\"": "[],", "\"otherOptions\"": "{},", "\"webhookId\"": "\"c8f49f36-7ab6-4607-bc5a-41c9555ebd09\",", "\"event\"": "\"emailsReplied\",", "\"isFirst\"": "true", "\"prompt\"": "\"=The following is a list of emails and the categories they fall into:\\nCategories=[\\\"interested\\\", \\\"Out of office\\\", \\\"unsubscribe\\\", \\\"other\\\"]\\n\\nInterested is when the reply is positive.\\\"\\n\\n{{$json[\\\"text\\\"].replaceAll(/^\\\\s+|\\\\s+$/g, '').replace(/(\\\\r\\\\n|\\\\n|\\\\r)/gm, \\\"\\\")}}\\\\\\\"\\nCategory:\",", "\"topP\"": "1,", "\"maxTokens\"": "6,", "\"temperature\"": "0", "\"openAiApi\"": "{", "\"connections\"": "{", "\"Merge\"": "{", "\"main\"": "[", "\"node\"": "\"follow up task\",", "\"index\"": "0", "\"OpenAI\"": "{", "\"Switch\"": "{", "\"HubSpot - Create Deal\"": "{", "\"Lemlist - Lead Replied\"": "{", "\"HubSpot - Get contact ID\"": "{", "\"HubSpot - Get contact ID1\"": "{", "}slemlist <> GPT-3": "Supercharge your sales workflows"}