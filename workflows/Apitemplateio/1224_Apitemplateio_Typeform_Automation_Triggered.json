{"nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [490, 280], "webhookId": "6c4b1aa0-226a-4875-bdc3-85bf2313085b", "parameters": {"formId": "dpr2kxSL", "simplifyAnswers": false}, "credentials": {"typeformApi": "Typeform Burner Account"}, "typeVersion": 1}, {"name": "APITemplate.io", "type": "n8n-nodes-base.apiTemplateIo", "position": [690, 280], "parameters": {"options": {"fileName": "invoice.pdf"}, "download": true, "resource": "pdf", "pdfTemplateId": "96c77b2b1ab6ac88", "jsonParameters": true, "propertiesJson": "={\n  \"company\": \"n8n\",\n  \"email\": \"{{$json[\"1\"][\"email\"]}}\",\n  \"invoice_no\": \"*********\",\n  \"invoice_date\": \"18-03-2021\",\n  \"invoice_due_date\": \"17-04-2021\",\n  \"address\": \"Berlin, Germany\",\n  \"company_bill_to\": \"{{$json[\"0\"][\"text\"]}}\",\n  \"website\": \"https://n8n.io\",\n  \"document_id\": \"************\",\n  \"items\": [\n    {\n      \"item_name\": \"{{$json[\"2\"][\"text\"]}}\",\n      \"price\": \"EUR {{$json[\"3\"][\"number\"]}}\"\n    },\n    {\n      \"item_name\": \"{{$json[\"4\"][\"text\"]}}\",\n      \"price\": \"EUR {{$json[\"5\"][\"number\"]}}\"\n    }    \n    ]\n}"}, "credentials": {"apiTemplateIoApi": "APITemplate Credentials"}, "typeVersion": 1}], "connections": {"Typeform Trigger": {"main": [[{"node": "APITemplate.io", "type": "main", "index": 0}]]}}}