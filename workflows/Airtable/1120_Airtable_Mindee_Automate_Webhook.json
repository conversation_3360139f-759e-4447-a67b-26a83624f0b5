{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 300], "webhookId": "39f1b81f-f538-4b94-8788-29180d5e4016", "parameters": {"path": "39f1b81f-f538-4b94-8788-29180d5e4016", "options": {"binaryData": true}, "httpMethod": "POST", "responseData": "allEntries", "responseMode": "lastNode", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": "Webhook Workflow Credentials"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.mindee", "position": [650, 300], "parameters": {"binaryPropertyName": "receipt"}, "credentials": {"mindeeReceiptApi": "expense-tracker"}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [850, 300], "parameters": {"table": "Receipt", "fields": ["category", "date", "currency", "locale", "merchant", "time", "total"], "options": {}, "operation": "append", "application": "appThOr4e97XjXcDu", "addAllFields": false}, "credentials": {"airtableApi": "Airtable Credentials n8n"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [1050, 300], "parameters": {"values": {"string": [{"name": "data", "value": "={{$json[\"fields\"]}}"}, {"name": "message", "value": "=You spent {{$json[\"fields\"][\"currency\"]}} {{$json[\"fields\"][\"total\"]}} on {{$json[\"fields\"][\"category\"]}} at {{$json[\"fields\"][\"merchant\"]}} on {{$json[\"fields\"][\"date\"]}} at {{$json[\"fields\"][\"time\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "connections": {"Mindee": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}