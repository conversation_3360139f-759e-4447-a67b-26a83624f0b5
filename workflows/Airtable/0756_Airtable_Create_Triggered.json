{"meta": {"instanceId": "db80165df40cb07c0377167c050b3f9ab0b0fb04f0e8cae0dc53f5a8527103ca", "templateCredsSetupCompleted": true}, "nodes": [{"id": "ed5363cf-1fb6-4662-b12c-073b2b3a3576", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-240, 140], "webhookId": "ebe97b63-ae4b-40e7-9738-b7cf7ffbc8b6", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "e47a166f-3e70-433e-ad0d-2100309cac92", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-60, 500], "parameters": {"options": {"topP": 1}, "modelName": "models/gemini-2.0-flash-lite"}, "credentials": {"googlePalmApi": {"id": "Xp5T9q3YYxBIw2nd", "name": "Google Gemini(PaLM) Api account✅"}}, "typeVersion": 1}, {"id": "5474805f-8d18-4a09-a3ea-5602af97a5de", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [500, 360], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "d9a0eadc-54c7-4980-b4f8-79fd77627c32", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [600, 520], "parameters": {"jsonSchemaExample": "{\n\t\"name\": \"Name of the prompt\",\n    \"category\" : \"the prompt category\"\n}"}, "typeVersion": 1.2}, {"id": "898f64cd-2332-42ad-9bac-a817dd9bf3d7", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [220, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "9c5fec90-b7f0-45f3-81a3-22e0956fc3bf", "name": "text", "type": "string", "value": "={{ $json.text }}"}]}}, "typeVersion": 3.4}, {"id": "4bbd160a-98bd-4622-a54e-77b61ff91b46", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [380, 540], "parameters": {"options": {"topP": 1}, "modelName": "models/gemini-2.0-flash-lite"}, "credentials": {"googlePalmApi": {"id": "Xp5T9q3YYxBIw2nd", "name": "Google Gemini(PaLM) Api account✅"}}, "typeVersion": 1}, {"id": "f45cbed4-c2b8-4f1b-8026-4686324a714a", "name": "Return results", "type": "n8n-nodes-base.set", "position": [960, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "40aba86b-57b7-4c74-8e9f-d09cd2f344c5", "name": "text", "type": "string", "value": "={{ $('Generate a new prompt').item.json.text }}"}]}}, "typeVersion": 3.4}, {"id": "25650ec5-b559-4bfc-a95a-f81c674bc680", "name": "Categorize and name Prompt", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [360, 140], "parameters": {"text": "={{ $json.text }}", "messages": {"messageValues": [{"message": "=Categorize the above prompt into a category that it can fall into"}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "c324d952-0722-40aa-981c-fcb2007b43b9", "name": "set prompt fields", "type": "n8n-nodes-base.set", "position": [660, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "cbf3b587-67fd-4f08-b50f-53561e869827", "name": "name", "type": "string", "value": "={{ $json.output.name }}"}, {"id": "7fda5833-9a3b-4c8a-b18d-4c31b35dae94", "name": "category", "type": "string", "value": "={{ $json.output.category }}"}, {"id": "50f06ab3-97d5-43cb-83ff-1a6aac45251b", "name": "Prompt", "type": "string", "value": "={{ $('Edit Fields').item.json.text }}"}]}}, "typeVersion": 3.4}, {"id": "97ad8d84-141e-4c21-8ce4-930dbe921f76", "name": "add to airtable", "type": "n8n-nodes-base.airtable", "position": [800, 140], "parameters": {"base": {"__rl": true, "mode": "list", "value": "app994hU3fOw0ssrx", "cachedResultUrl": "https://airtable.com/app994hU3fOw0ssrx", "cachedResultName": "Prompt Library"}, "table": {"__rl": true, "mode": "list", "value": "tbldwJrCK2HmAeknA", "cachedResultUrl": "https://airtable.com/app994hU3fOw0ssrx/tbldwJrCK2HmAeknA", "cachedResultName": "Prompt Library"}, "columns": {"value": {"Name": "={{ $json.name }}", "Prompt": "={{ $json.Prompt }}", "Category": "={{ $json.category }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Prompt", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Created ON", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Created ON", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Updated", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "Updated", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Category", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Category", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "create"}, "credentials": {"airtableTokenApi": {"id": "CAa937hASXcJZWTv", "name": "Airtable Personal Access Token account✅"}}, "typeVersion": 2.1}, {"id": "516dc434-25d9-4011-9453-bb28521823ca", "name": "Generate a new prompt", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-80, 140], "parameters": {"messages": {"messageValues": [{"message": "=You are an **expert n8n prompt engineer**, specializing in creating highly optimized, context-aware prompts for AI agents in n8n workflows. Your primary goal is to ensure AI agents execute well-defined tasks **accurately, autonomously, and efficiently**.  \n\n### Instructions  \n1. **Define the AI Agent's Role and Rules**  \n   - Use a structured role definition format:  \n     `\"You are a [SPECIFIC ROLE] working for [SPECIFIC BUSINESS CONTEXT].\"`  \n   - Clearly specify the agent's responsibilities and scope.  \n\n2. **Provide Task Instructions**  \n   - Use a **step-by-step** numbered list to outline the process.  \n   - Ensure the instructions allow for flexibility but prevent errors.  \n\n3. **Set Rules to Guide AI Behavior**  \n   - Enumerate key constraints such as:  \n     - Timezone requirements  \n     - Prohibitions on making assumptions  \n     - Required formatting for responses  \n\n4. **Use Few-Shot Prompting**  \n   - Provide clear examples of desired outputs inside `<example>` tags.  \n\n5. **Include Additional Context**  \n   - Define relevant business details, the current date/time, and any required environmental context.  \n\n---\n\n## Input Layer  \n### Structuring User Inputs  \n1. **Define Input Type**  \n   - Specify whether inputs come from a human user (chat-based) or an external system (API calls).  \n\n2. **Handle Dynamic Inputs**  \n   - Use placeholders (e.g., `{customer_name}`, `{appointment_date}`) for adaptable prompts.  \n\n3. **Ensure Personalization**  \n   - Format prompts naturally while maintaining clarity and specificity.  \n\n4. **Merge Static & Dynamic Data**  \n   - Concatenate fixed prompt structures with real-time system data from n8n.  \n\n---\n## Action Layer  \n### Tool and Function Calling  \n1. **Standardized Tool Naming**  \n   - Use `snake_case` names for tools (e.g., `check_calendar_availability`).  \n\n2. **Provide Clear Tool Descriptions**  \n   - Example:  \n     `\"Use the `fetch_customer_data` tool to retrieve details about a specific user based on their email address.\"`  \n\n3. **Specify Tool Parameters & Expected Responses**  \n   - Define required inputs, expected formats, and error handling strategies.  \n\n4. **Avoid Hallucinations**  \n   - AI should **only** use tools for their defined purposes. If information is missing, request clarification instead of guessing.  \n\n---\n## Example Prompt for an AI Agent in n8n  \n\n```yaml\n# System Layer\n## Role\nYou are a **Scheduling Assistant** working for a **beauty salon**. Your role is to help customers book appointments.  \n\n## Instructions\n1. Ask the user for their preferred appointment date.  \n2. Use `check_calendar_availability` to find open slots.  \n3. If no slots are available, ask the user to select another day.  \n4. Capture the user’s **full name** and **email**.  \n5. Use `create_calendar_appointment` to confirm the booking.  \n6. Notify the user with appointment details.  \n\n## Rules\n- Always use **UTC+1 timezone**.  \n- Do not assume details—ask if unsure.  \n- If asked about non-scheduling topics, respond: `\"I can only assist with booking appointments.\"`  \n\n## Few-shot Example  \n<example>\n\"I have successfully booked your appointment:\n- Date & Time: **Wednesday, 15 March 2025, 14:00 (UTC+1)**\n- Booking Email: **<EMAIL>**\nIf you need to cancel, please call +**************.\"\n</example>\n```\n---\n## Key Considerations  \n✅ **Avoid vague roles** (e.g., \"You are an assistant\"). Always specify **business context**.  \n✅ **Keep task steps structured** but flexible.  \n✅ **Provide explicit tool instructions** in a separate section.  \n✅ **Enable AI to ask clarifying questions** instead of making assumptions.  \n✅ **Use examples to guide expected outputs.**  \n\n\n"}]}}, "typeVersion": 1.5}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "Categorize and name Prompt", "type": "main", "index": 0}]]}, "add to airtable": {"main": [[{"node": "Return results", "type": "main", "index": 0}]]}, "set prompt fields": {"main": [[{"node": "add to airtable", "type": "main", "index": 0}]]}, "Generate a new prompt": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Generate a new prompt", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Categorize and name Prompt", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Categorize and name Prompt", "type": "ai_languageModel", "index": 0}, {"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Categorize and name Prompt": {"main": [[{"node": "set prompt fields", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Generate a new prompt", "type": "main", "index": 0}]]}}}