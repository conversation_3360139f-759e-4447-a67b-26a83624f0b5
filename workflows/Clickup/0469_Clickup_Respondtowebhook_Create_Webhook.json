{"meta": {"instanceId": "1e5c69f0bf3f7484ac715feadbdb5d46fa5fa304d6cf822da9bd609721d1fee8"}, "nodes": [{"id": "c39381ac-4795-4408-9383-7bae62755569", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1580, 640], "parameters": {"options": {}, "respondWith": "text", "responseBody": "=Task Created: ID  {{ $json.id }}"}, "typeVersion": 1}, {"id": "ff72f0cb-1ea2-41e5-8f9f-7aa7ce994632", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, 500], "parameters": {"color": 4, "width": 874, "height": 359, "content": "## Create new tasks to airtable from a slack command"}, "typeVersion": 1}, {"id": "263f6c3b-5225-4d3f-a8ce-5052946b4251", "name": "Receives slack command", "type": "n8n-nodes-base.webhook", "position": [960, 640], "webhookId": "09d30853-66a3-4494-ba4b-115d28402811", "parameters": {"path": "09d30853-66a3-4494-ba4b-115d28402811/slackcommand", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "bbb46ec6-0b43-4a15-b12a-5e5d4b8d6c3d", "name": "Set your nodes", "type": "n8n-nodes-base.set", "position": [1160, 640], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8f6664ce-a3ad-42fb-84f7-58608d3c0ce8", "name": "channel_name", "type": "string", "value": "={{ $json.body.channel_name }}"}, {"id": "54bf76f5-f00a-4f8e-bfcb-addd8af75a1a", "name": "command", "type": "string", "value": "={{ $json.body.command }}"}, {"id": "37e273c0-2775-420b-9eb2-baeab3d1fdb6", "name": "user_name", "type": "string", "value": "={{ $json.body.user_name }}"}, {"id": "6926bdae-e5eb-429d-a17d-7775b87184b1", "name": "text", "type": "string", "value": "={{ $json.body.text }}"}]}}, "typeVersion": 3.3}, {"id": "f8b66cdb-3c56-4ec6-b2a2-f3fab8ba392c", "name": "Create new clickup task", "type": "n8n-nodes-base.clickUp", "position": [1340, 640], "parameters": {"list": "************", "name": "={{ $json.text }}", "team": "**********", "space": "***********", "folderless": true, "authentication": "oAuth2", "additionalFields": {"content": "={{ $json.text }}", "assignees": []}}, "credentials": {"clickUpOAuth2Api": {"id": "Cs34tMBCqaT1yt1w", "name": "ClickUp account"}}, "typeVersion": 1}, {"id": "47aa82ae-8a9c-40fa-be79-2bd602ffa045", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, 300], "parameters": {"width": 467, "height": 861.*************, "content": "## Create new Clickup Tasks from Slack commands\nThis workflow aims to make it easy to create new tasks on Clickup from normal Slack messages using simple slack command. \n\nFor example We can have a slack command as \n\n/newTask Set task to update new contacts on CRM and assign them to the sales team\nThis will have an new task on Clickup with the same title and description on Clickup \n\nFor most teams, getting tasks from <PERSON>lack to <PERSON>lickup involves manually entering the new tasks into Clickup. What if we could do this with a simple slash command?\n\n## Step 1\nThe first step is to Create an endpoint URL for your slack command by creating an events API from the link [below] https://api.slack.com/apps/)\n\n## STEP 2 \nNext step is defining the endpoint for your URL\nCreate a new webhook endpoint from your n8n with a POST and paste the endpoint URL to your event API. This will send all slash commands associated with the Slash to the desired endpoint\n\n\nOnce you have tested the webhook slash command is working with the webhook, create a new Clickup API that can be used to create new tasks in ClickUp\n\nThis workflow creates a new task with the start dates on Clikup that can be assigned to the respective team members\n\nMore details about the document setup can be found on this document [below](https://docs.google.com/document/d/1jw_UP6sXmGsIMktW0Z-b-yQB1leDLatUY2393bA4z8s/edit?usp=sharing)\n\n   ####  Happy Productivity\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"Set your nodes": {"main": [[{"node": "Create new clickup task", "type": "main", "index": 0}]]}, "Receives slack command": {"main": [[{"node": "Set your nodes", "type": "main", "index": 0}]]}, "Create new clickup task": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}}