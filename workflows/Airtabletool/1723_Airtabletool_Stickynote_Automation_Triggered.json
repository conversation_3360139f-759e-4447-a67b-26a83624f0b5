{"id": "V8ypWn7oaOVS3zH0", "meta": {"instanceId": "1acdaec6c8e84424b4715cf41a9f7ec057947452db21cd2e22afbc454c8711cd", "templateCredsSetupCompleted": true}, "name": "AI Social Media Caption Creator", "tags": [], "nodes": [{"id": "12d0470e-1030-47c4-8bd0-890d5b3a5976", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [120, -120], "parameters": {"text": "={{ $json['Briefing'] }}", "options": {"systemMessage": "=<system_prompt>  \nYOU ARE AN EXPERT CAPTION CREATOR AGENT FOR INSTAGRAM, DESIGNED FOR USE IN N8N WORKFLOWS. YOUR TASK IS TO CREATE A CREATIVE, TARGET AUDIENCE-ORIENTED, AND <PERSON><PERSON><PERSON><PERSON><PERSON> CAPTION BASED ON THE BRIEFING: `{{ $json['Briefing'] }}`. YOU SHOULD RETRIEVE ADDITIONAL INFORMATION ABOUT THE TARGET AUDIENCE AND PREFERRED WORDING USING THE TOOL \"BACKGROUND INFO\" TO MAXIMIZE THE QUALITY AND RELEVANCE OF THE CAPTION.  \n\n###INSTRUCTIONS###  \n\n- YOU MUST:  \n  1. READ AND UNDERSTAND THE BRIEFING CAREFULLY.  \n  2. RETRIEVE ADDITIONAL DATA ABOUT THE TARGET AUDIENCE AND COMMUNICATION STYLE USING THE \"BACKGROUND INFO\" TOOL.  \n  3. CREATE A CAPTION THAT IS CREATIVE, ENG<PERSON><PERSON>, AND TAILORED TO THE TARGET AUDIENCE.  \n  4. <PERSON><PERSON><PERSON><PERSON> THAT THE CAPTION INCLUDES A CLEAR CALL-TO-ACTION (CTA) THAT ENCOURAGES USERS TO TAKE ACTION (E.G., LIKE, COMMENT, OR CLICK).  \n  5. OUTPUT ONLY THE FINAL CAPTION WITHOUT ANY ACCOMPANYING EXPLANATIONS, FEEDBACK, OR COMMENTS.  \n\n###CHAIN OF THOUGHTS###  \n\n1. **UNDERSTANDING THE BRIEFING**:  \n   - THOROUGHLY READ THE BRIEFING PROVIDED UNDER `{{ $json['Briefing/Notizen'] }}`.  \n   - IDENTIFY THE MAIN FOCUS OF THE POST (E.G., PRODUCT PROMOTION, INSPIRATION, INFORMATION). \n   - NOTE THE KEY THEMES, MOOD, AND DESIRED IMPACT.  \n\n2. **TARGET AUDIENCE ANALYSIS**:  \n   - USE THE \"BACKGROUND INFO\" TOOL TO:  \n     - RETRIEVE THE TARGET AUDIENCE'S AGE, INTERESTS, AND NEEDS.  \n     - DEFINE THE APPROPRIATE TONE (FRIENDLY, PROFESSIONAL, INSPIRATIONAL, ETC.).  \n\n3. **CREATIVE CAPTION DEVELOPMENT**:  \n   - DEVELOP AN OPENING SENTENCE THAT GRABS THE TARGET AUDIENCE'S ATTENTION.  \n   - WRITE A BODY THAT CONVEYS THE CORE MESSAGE OF THE POST AND RESONATES WITH THE TARGET AUDIENCE.  \n   - ADD AN INVITING CTA (E.G., \"What do you think? Share your thoughts in the comments!\" OR \"Click the link in our bio!\").  \n\n4. **FINALIZATION**:  \n   - CHECK THE CAPTION FOR CLARITY, CONSISTENCY, AND GRAMMAR.  \n   - ENSURE THAT IT ALIGNS WITH THE TARGET AUDIENCE AND THE IDENTIFIED TONE.  \n   - MAXIMIZE CREATIVITY AND ENTERTAINMENT VALUE WITHOUT LOSING THE ESSENTIAL MESSAGE.  \n\n5. **OUTPUT**:  \n   - OUTPUT ONLY THE FINAL CAPTION WITHOUT ANY ACCOMPANYING COMMENTS, FEEDBACK, OR EXPLANATIONS.  \n\n###WHAT NOT TO DO###  \n\n- **DO NOT OUTPUT ANY ACCOMPANYING TEXTS, EXPLANATIONS, OR FEEDBACK** ABOUT THE CAPTION.  \n- **DO NOT WORK WITHOUT PRIOR TARGET AUDIENCE ANALYSIS**.  \n- **DO NOT USE CLICHÉ PHRASES** THAT HAVE NO RELEVANCE TO THE TARGET AUDIENCE.  \n- **DO NOT ALLOW ANY SPELLING OR GRAMMATICAL ERRORS**.  \n\n</system_prompt>\n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "3a6fcc4e-46ed-4f80-a9ce-f955e3d47222", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [80, 100], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "EjchNb5GBqYh0Cqn", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "1a8b6f44-b9cf-4c80-ac5d-358d7cf61404", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [220, 100], "parameters": {"sessionKey": "={{ $json.id }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "a4972690-5fa5-48bd-b5fd-b1899076b6c0", "name": "Get Airtable Record Data", "type": "n8n-nodes-base.airtable", "position": [-40, -120], "parameters": {"id": "={{ $json.id }}", "base": {"__rl": true, "mode": "list", "value": "appXvZviYORVbPEaS", "cachedResultUrl": "https://airtable.com/appXvZviYORVbPEaS", "cachedResultName": "Redaktionsplan 2025 - E&P Reisen"}, "table": {"__rl": true, "mode": "list", "value": "tbllbO3DyTNie9Pga", "cachedResultUrl": "https://airtable.com/appLe3fQHeaRN7kWG/tbllbO3DyTNie9Pga", "cachedResultName": "Redaktionsplanung"}, "options": {}}, "credentials": {"airtableTokenApi": {"id": "pMphGrxsDsELetHZ", "name": "Airtable account"}}, "typeVersion": 2.1}, {"id": "27519b09-7ce7-4a8b-abe7-dc630eea24b0", "name": "Wait 1 Minute", "type": "n8n-nodes-base.wait", "position": [-200, -120], "webhookId": "757986ac-2e3f-4a5b-993d-b53b8ae12258", "parameters": {"unit": "minutes", "amount": 1}, "typeVersion": 1.1}, {"id": "b9e7c19a-e468-4f83-b1a4-2013af36caa0", "name": "Format Fields", "type": "n8n-nodes-base.set", "position": [440, -120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c7243724-463f-4732-8866-efdf19837f17", "name": "SoMe Text", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "5d4e6149-20a5-42bf-be6b-6ebaa31c517e", "name": "Post Caption into Airtable Record", "type": "n8n-nodes-base.airtable", "position": [600, -120], "parameters": {"base": {"__rl": true, "mode": "list", "value": "appXvZviYORVbPEaS", "cachedResultUrl": "https://airtable.com/appXvZviYORVbPEaS", "cachedResultName": "Redaktionsplan 2025 - E&P Reisen"}, "table": {"__rl": true, "mode": "list", "value": "tblxsKj5PtumCR9um", "cachedResultUrl": "https://airtable.com/appXvZviYORVbPEaS/tblxsKj5PtumCR9um", "cachedResultName": "Redaktionsplanung"}, "columns": {"value": {"id": "={{ $('Get Airtable Record Data').item.json.id }}", "Posten": false, "SoMe_Text_KI": "={{ $json['SoMe Text'] }}", "Werbeanzeige": false}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Beitragsname", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Beitragsname", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "options", "display": true, "options": [{"name": "E&P", "value": "E&P"}, {"name": "SER", "value": "SER"}, {"name": "SBW", "value": "SBW"}, {"name": "SZO", "value": "SZO"}, {"name": "UCH", "value": "UCH"}], "removed": false, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Netzwerk", "type": "array", "display": true, "options": [{"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "Threads", "value": "Threads"}, {"name": "TikTok", "value": "TikTok"}, {"name": "YouTube Shorts", "value": "YouTube Shorts"}, {"name": "MyBusiness", "value": "MyBusiness"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "WhatsApp", "value": "WhatsApp"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "SBW", "value": "SBW"}], "removed": false, "readOnly": false, "required": false, "displayName": "Netzwerk", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "options", "display": true, "options": [{"name": "Brainstorming", "value": "Brainstorming"}, {"name": "Bitte formulieren", "value": "Bitte formulieren"}, {"name": "Bitte checken/freigeben", "value": "Bitte checken/freigeben"}, {"name": "Bitte ändern", "value": "Bitte ändern"}, {"name": "Warten auf externe Rückmeldung", "value": "Warten auf externe Rückmeldung"}, {"name": "Freigabe erteilt/Bitte einplanen", "value": "Freigabe erteilt/Bitte einplanen"}, {"name": "Geplant/Veröffentlicht", "value": "Geplant/Veröffentlicht"}], "removed": false, "readOnly": false, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Zuständigkeit", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Zuständigkeit", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "KW", "type": "options", "display": true, "options": [{"name": "KW 1", "value": "KW 1"}, {"name": "KW 2", "value": "KW 2"}, {"name": "KW 3", "value": "KW 3"}, {"name": "KW 4", "value": "KW 4"}, {"name": "KW 5", "value": "KW 5"}, {"name": "KW 6", "value": "KW 6"}, {"name": "KW 7", "value": "KW 7"}, {"name": "KW 8", "value": "KW 8"}, {"name": "KW 9", "value": "KW 9"}, {"name": "KW 10", "value": "KW 10"}, {"name": "KW 11", "value": "KW 11"}, {"name": "KW 12", "value": "KW 12"}, {"name": "KW 13", "value": "KW 13"}, {"name": "KW 14", "value": "KW 14"}, {"name": "KW 15", "value": "KW 15"}, {"name": "KW 16", "value": "KW 16"}, {"name": "KW 17", "value": "KW 17"}, {"name": "KW 18", "value": "KW 18"}, {"name": "KW 19", "value": "KW 19"}, {"name": "KW 20", "value": "KW 20"}, {"name": "KW 21", "value": "KW 21"}, {"name": "KW 22", "value": "KW 22"}, {"name": "KW 23", "value": "KW 23"}, {"name": "KW 24", "value": "KW 24"}, {"name": "KW 25", "value": "KW 25"}, {"name": "KW 26", "value": "KW 26"}, {"name": "KW 27", "value": "KW 27"}, {"name": "KW 28", "value": "KW 28"}, {"name": "KW 29", "value": "KW 29"}, {"name": "KW 30", "value": "KW 30"}, {"name": "KW 31", "value": "KW 31"}, {"name": "KW 32", "value": "KW 32"}, {"name": "KW 33", "value": "KW 33"}, {"name": "KW 34", "value": "KW 34"}, {"name": "KW 35", "value": "KW 35"}, {"name": "KW 36", "value": "KW 36"}, {"name": "KW 37", "value": "KW 37"}, {"name": "KW 38", "value": "KW 38"}, {"name": "KW 39", "value": "KW 39"}, {"name": "KW 40", "value": "KW 40"}, {"name": "KW 41", "value": "KW 41"}, {"name": "KW 42", "value": "KW 42"}, {"name": "KW 43", "value": "KW 43"}, {"name": "KW 44", "value": "KW 44"}, {"name": "KW 45", "value": "KW 45"}, {"name": "KW 46", "value": "KW 46"}, {"name": "KW 47", "value": "KW 47"}, {"name": "KW 48", "value": "KW 48"}, {"name": "KW 49", "value": "KW 49"}, {"name": "KW 50", "value": "KW 50"}, {"name": "KW 51", "value": "KW 51"}, {"name": "KW 52", "value": "KW 52"}, {"name": "47", "value": "47"}], "removed": false, "readOnly": false, "required": false, "displayName": "KW", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Veröffentlichungsdatum SoMe", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Veröffentlichungsdatum SoMe", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Destination/Haus", "type": "array", "display": true, "options": [{"name": "1: Allgemein", "value": "1: Allgemein"}, {"name": "Ahrntal - Bruggerhof", "value": "Ahrntal - Bruggerhof"}, {"name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>"}, {"name": "Ahrntal - Griesfeld", "value": "Ahrntal - Griesfeld"}, {"name": "Davos - Allgemein", "value": "Davos - Allgemein"}, {"name": "Davos - Schweizerhaus", "value": "Davos - Schweizerhaus"}, {"name": "Davos - <PERSON><PERSON><PERSON><PERSON>", "value": "Davos - <PERSON><PERSON><PERSON><PERSON>"}, {"name": "Davos - Waldschlössli", "value": "Davos - Waldschlössli"}, {"name": "Kleinwalsertal - Heuberghaus", "value": "Kleinwalsertal - Heuberghaus"}, {"name": "L2A - SZO", "value": "L2A - SZO"}, {"name": "L2A - UCH", "value": "L2A - UCH"}, {"name": "Lenzerheide - Jenatsch", "value": "Lenzerheide - Jenatsch"}, {"name": "Montafon <PERSON> <PERSON><PERSON><PERSON>", "value": "Montafon <PERSON> <PERSON><PERSON><PERSON>"}, {"name": "Montafon - Klein Tirol", "value": "Montafon - Klein Tirol"}, {"name": "PdS - Jolimont", "value": "PdS - Jolimont"}, {"name": "PdS - Victoria", "value": "PdS - Victoria"}, {"name": "Saalbach - Allgemein", "value": "Saalbach - Allgemein"}, {"name": "Saalbach - Steinachhof", "value": "Saalbach - Steinachhof"}, {"name": "Schweiz - Allgemein", "value": "Schweiz - Allgemein"}, {"name": "Stubaital <PERSON> <PERSON><PERSON>", "value": "Stubaital <PERSON> <PERSON><PERSON>"}, {"name": "Team", "value": "Team"}, {"name": "VT - SBW", "value": "VT - SBW"}, {"name": "SurfZone", "value": "SurfZone"}, {"name": "lenzerheide - allgemein", "value": "lenzerheide - allgemein"}, {"name": "Family", "value": "Family"}, {"name": "Jobs", "value": "Jobs"}, {"name": "L2A", "value": "L2A"}, {"name": "Davos - Spinabad", "value": "Davos - Spinabad"}], "removed": false, "readOnly": false, "required": false, "displayName": "Destination/Haus", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Content Art", "type": "options", "display": true, "options": [{"name": "Bild(er)", "value": "Bild(er)"}, {"name": "Video", "value": "Video"}, {"name": "Carousel", "value": "Carousel"}, {"name": "Story", "value": "Story"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"name": "Link", "value": "Link"}, {"name": "Bilderalbum", "value": "Bilderalbum"}, {"name": "Text", "value": "Text"}, {"name": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "removed": false, "readOnly": false, "required": false, "displayName": "Content Art", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Briefing/Notizen", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Briefing/Notizen", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Linkmanager-Link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Linkmanager-Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Short Link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Short Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Story", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Story", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MyBusiness Link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "MyBusiness Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SoMe Text", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "SoMe Text", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON> Hashtags", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON><PERSON> Hashtags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "<PERSON><PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SoMe Media", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "SoMe Media", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Link zum Canva Layout", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Link zum Canva Layout", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Storylink Canva", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Storylink Canva", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "MyBusiness Layout", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "MyBusiness Layout", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "<PERSON><PERSON>", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "created_at", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "created_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SoMe_Text_KI", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "SoMe_Text_KI", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Werbeanzeige", "type": "boolean", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Werbeanzeige", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id", "SoMe_Text_KI"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update"}, "credentials": {"airtableTokenApi": {"id": "pMphGrxsDsELetHZ", "name": "Airtable account"}}, "typeVersion": 2.1}, {"id": "ddc9159e-0da7-4844-84c5-eca981b9d52f", "name": "Airtable Trigger: New Record", "type": "n8n-nodes-base.airtableTrigger", "position": [-360, -120], "parameters": {"baseId": {"__rl": true, "mode": "id", "value": "appXvZviYORVbPEaS"}, "tableId": {"__rl": true, "mode": "id", "value": "tblxsKj5PtumCR9um"}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerField": "created_at", "authentication": "airtableTokenApi", "additionalFields": {}}, "credentials": {"airtableTokenApi": {"id": "pMphGrxsDsELetHZ", "name": "Airtable account"}}, "typeVersion": 1}, {"id": "a71626b0-43ba-430b-bd2f-8cc121676e46", "name": "Background Info", "type": "n8n-nodes-base.airtableTool", "position": [360, 100], "parameters": {"id": "reckd97lgylz93Ht5", "base": {"__rl": true, "mode": "list", "value": "appXvZviYORVbPEaS", "cachedResultUrl": "https://airtable.com/appXvZviYORVbPEaS", "cachedResultName": "Redaktionsplan 2025 - E&P Reisen"}, "table": {"__rl": true, "mode": "list", "value": "tblMmE9cjgNZCoIO1", "cachedResultUrl": "https://airtable.com/appLe3fQHeaRN7kWG/tblMmE9cjgNZCoIO1", "cachedResultName": "Good to know"}, "options": {}, "descriptionType": "manual", "toolDescription": "Read data from Airtable"}, "credentials": {"airtableTokenApi": {"id": "pMphGrxsDsELetHZ", "name": "Airtable account"}}, "typeVersion": 2.1}, {"id": "9c422e74-155c-4714-87aa-16b31bd73e5b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-680, 80], "parameters": {"width": 660, "height": 680, "content": "# Welcome to my AI Social Media Caption Creator Workflow!\n\nThis workflow automatically creates a social media post caption in an editorial plan in Airtable. It also uses background information on the target group, tonality, etc. stored in Airtable.\n\n## This workflow has the following sequence:\n\n1. Airtable trigger (scan for new records every minute)\n2. Wait 1 Minute so the Airtable record creator has time to write the Briefing field\n3. retrieval of Airtable record data\n4. AI Agent to write a caption for a social media post. The agent is instructed to use background information stored in Airtable (such as target group, tonality, etc.) to create the post.\n5. Format the output and assign it to the correct field in Airtable.\n6. Post the caption into Airtable record.\n\n## The following accesses are required for the workflow:\n- Airtable Database: [Documentation](https://docs.n8n.io/integrations/builtin/credentials/airtable)\n- AI API access (e.g. via OpenAI, Anthropic, Google or Ollama)\n\n### Example of an editorial plan in Airtable: https://airtable.com/appIXeIkDPjQefHXN/shrwcY45g48RpcvvC\nFor this workflow you need the Airtable fields \"created_at\", \"Briefing\" and \"SoMe_Text_AI\"\n\nYou can contact me via LinkedIn, if you have any questions: https://www.linkedin.com/in/friedemann-schuetz"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "50376a31-f279-4f5d-9204-82cacb596751", "connections": {"AI Agent": {"main": [[{"node": "Format Fields", "type": "main", "index": 0}]]}, "Format Fields": {"main": [[{"node": "Post Caption into Airtable Record", "type": "main", "index": 0}]]}, "Wait 1 Minute": {"main": [[{"node": "Get Airtable Record Data", "type": "main", "index": 0}]]}, "Background Info": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Get Airtable Record Data": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Airtable Trigger: New Record": {"main": [[{"node": "Wait 1 Minute", "type": "main", "index": 0}]]}}}