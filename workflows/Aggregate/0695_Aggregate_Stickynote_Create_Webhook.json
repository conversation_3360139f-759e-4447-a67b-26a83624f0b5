{"meta": {"instanceId": "95b3ab5a70ab1c8c1906357a367f1b236ef12a1409406fd992f60255f0f95f85"}, "nodes": [{"id": "9e4a97c9-65dc-4be1-bd9d-d5e84ffedd69", "name": "Subscribe contact in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "This node subscribes the formatted contact data to a specific KlickTipp list.", "position": [900, 340], "parameters": {"email": "={{ $('New submission via Gravityforms').item.json.body['4'] }}", "fields": {"dataFields": [{"fieldId": "fieldFirstName", "fieldValue": "={{ $('New submission via Gravityforms').item.json.body['1'] }}"}, {"fieldId": "fieldLastName", "fieldValue": "={{ $('New submission via Gravityforms').item.json.body['3'] }}"}, {"fieldId": "fieldBirthday", "fieldValue": "={{ $json.birthday }}"}, {"fieldId": "field214512", "fieldValue": "={{ $('New submission via Gravityforms').item.json.body['7'] }}"}, {"fieldId": "field214514", "fieldValue": "={{ $json.webinar_rating }}"}, {"fieldId": "field214515", "fieldValue": "={{ $('New submission via Gravityforms').item.json.body['9'] }}"}, {"fieldId": "field214516", "fieldValue": "={{ $('New submission via Gravityforms').item.json.body['12.1'] }}"}, {"fieldId": "field214513", "fieldValue": "={{ $json.webinar_choice }}"}]}, "listId": "358895", "resource": "subscriber", "operation": "subscribe", "smsNumber": "={{ $json.mobile_number }}"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "a6cc678f-b8bf-4dc9-a9f5-3<PERSON><PERSON>e44d3b", "name": "Convert and set feedback data", "type": "n8n-nodes-base.set", "notes": "This node transforms the form data from Gravity Forms into the appropriate format required for the KlickTipp API.", "position": [680, 340], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f1263cb6-654a-4d07-9073-c015b720e6b7", "name": "mobile_number", "type": "string", "value": "={{ \n// Converts a phone number to numeric-only format with international code prefixed by \"00\"\n$json.body['5'] \n    ? $json.body['5']\n        .replace(/^\\+/, '00')   // Replace leading \"+\" with \"00\"\n        .replace(/[^0-9]/g, '') // Remove non-numeric characters\n    : ''\n}}"}, {"id": "b09cc146-e614-478a-8f33-324d813e0120", "name": "birthday", "type": "string", "value": "={{ \n// Converts a date to a UNIX timestamp (in seconds)\nMath.floor(\n    new Date($json.body['6'] + 'T00:00:00').getTime() / 1000\n)\n}}"}, {"id": "1c455eb9-0750-4d69-9dab-390847a3d582", "name": "webinar_choice", "type": "string", "value": "={{ \n  // Convert the date format from \"DD.MM.YYYY HH:mm\" to \"MM/DD/YYYY HH:mm\"\n  Math.floor(new Date($json[\"body\"][\"13\"].replace(\n    /(\\d{2})\\.(\\d{2})\\.(\\d{4})/, // Match the pattern \"DD.MM.YYYY\"\n    \"$2/$1/$3\" // Rearrange to \"MM/DD/YYYY\" (needed for JavaScript Date parsing)\n  )).getTime() / 1000) // Convert to milliseconds and divide by 1000 to get Unix timestamp (in seconds)\n}}"}, {"id": "e375b10b-b05f-413e-93ed-b835e009dd91", "name": "webinar_rating", "type": "string", "value": "={{\n// Multiplies the decimal number value by 100\n$json.body['8'] * 100 }}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "1f869f92-8e87-4ab5-8938-f327558ca73b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [880, 660], "parameters": {"width": 920, "height": 1182, "content": "### Introduction\nThis workflow facilitates seamless integration between Gravity Forms and KlickTipp, automating the process of handling customer feedback. By transforming raw form data into a format compatible with KlickTipp’s API, it eliminates manual data entry and ensures accurate, consistent information. The workflow relies on community nodes and is available exclusively for self-hosted n8n environments.\n\n### Benefits\n- **Efficient feedback management**: Automatically processes Gravity Forms submissions, saving time and ensuring timely data handling.\n- **Automation of workflows**: Launch follow-up actions like sending thank-you emails or surveys without manual intervention.\n- **Improved data accuracy**: Validates and transforms input data, minimizing errors and maintaining a professional database.\n\n### Key Features\n- **Gravity Forms Trigger**: Captures new form submissions using a webhook, including user feedback and preferences.\n- **Data Processing and Transformation**:\n  - Converts phone numbers to numeric-only format with international prefixes.\n  - Transforms date fields (e.g., birthdays) into UNIX timestamps.\n  - Scales numerical responses like feedback ratings to match desired formats.\n- **Subscriber Management in KlickTipp**: Adds or updates participants as subscribers in KlickTipp. Includes custom field mappings and tags, such as:\n  - Personal details (e.g., name, email, phone number).\n  - Feedback specifics (e.g., webinar ratings, selected sessions).\n  - Structured answers from Gravity Forms responses.\n  - Contact segmentation: Creates new tags based on form submission if necessary and adds these dynamic tags as well as fixed tags to contacts.\n- **Error Handling**: Ensures invalid or missing data does not disrupt the workflow, providing fallback values where needed.\n\n### Setup Instructions\n1. Set up the Webhook and KlickTipp nodes in your n8n instance.\n2. Connect your Webhook to Gravity Forms and authenticate your KlickTipp account.\n3. Create the necessary custom fields to match the data structure\n4. Verify and customize field assignments in the workflow to align with your specific form and subscriber list setup.\n\n![Source example](https://mail.cdndata.io/user/images/kt1073234/share_link_GravityForms_fields.png#full-width)\n\n### Testing and Deployment\n1. Test the workflow by submitting a form through Gravity Forms.\n2. Verify that the data is correctly processed and updated in KlickTipp.\n3. Simulate various scenarios (e.g., missing or invalid data) to ensure robust error handling.\n\n- **Customization**: Update field mappings within the KlickTipp nodes to ensure alignment with your specific account setup.  \n\n"}, "typeVersion": 1}, {"id": "b2206acf-c3e1-40bc-b268-7a7b89506f5d", "name": "Tag contact directly in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Applies existing tags to a subscriber in KlickTipp. This enables the use of specific signatures, sign out automations as well as the automation of emails and campaigns or other automations.", "position": [2620, 240], "parameters": {"email": "={{ $('New submission via Gravityforms').item.json.body['4'] }}", "tagId": "={{$json.tag_ids}}", "resource": "contact-tagging"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "a143bed3-a63b-4759-b249-a1cb0683c22a", "name": "Tag creation check", "type": "n8n-nodes-base.if", "notes": "This node checks the result of the tag comparison and branches the workflow accordingly in order to directly tag the contact or to create the tag first and to then follow through with the tagging.", "position": [1920, 340], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d9567816-9236-434d-b46e-e47f4d36f289", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.exist }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2.2}, {"id": "9cac27ed-0fa7-4e80-84da-4d9f5bae7d72", "name": "Aggregate tags to add to contact", "type": "n8n-nodes-base.aggregate", "notes": "This node aggregates all IDs of the existing tags to a list.", "position": [2420, 240], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tag_ids", "fieldToAggregate": "tag_id"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "7f72f6ca-e13f-4f66-a8c9-c9efee511d84", "name": "Create the tag in KlickTipp", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Creates a new tag in KlickTipp if it does not already exist.", "position": [2220, 460], "parameters": {"name": "={{ $json.name }}", "operation": "create"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "b44fe73c-011e-4dee-9961-e8221d577140", "name": "Aggregate array of created tags", "type": "n8n-nodes-base.aggregate", "notes": "This node aggregates all IDs of the newly created tags to a list.", "position": [2420, 460], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"renameField": true, "outputFieldName": "tag_ids", "fieldToAggregate": "id"}]}}, "notesInFlow": true, "typeVersion": 1}, {"id": "a03ba56c-1470-48c4-a3ea-aa7d282e5e37", "name": "Tag contact KlickTipp after trag creation", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "Associates a specific tag with a subscriber in KlickTipp using their email address. This enables the use of specific signatures, signout automations as well as the automation of emails and campaigns or other automations.", "position": [2620, 460], "parameters": {"email": "={{ $('New submission via Gravityforms').item.json.body['4'] }}", "tagId": "={{$json.tag_ids}}", "resource": "contact-tagging"}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "605a93b4-1ebf-4436-8aad-ea433e4bf5bf", "name": "Get list of all existing tags", "type": "n8n-nodes-klicktipp.klicktipp", "notes": "This node fetches all tags that already exist in KlickTipp.", "position": [1280, 460], "parameters": {}, "credentials": {"klickTippApi": {"id": "K9JyBdCM4SZc1cXl", "name": "DEMO KlickTipp account"}}, "notesInFlow": true, "typeVersion": 2}, {"id": "b17669be-62b3-423d-8018-dc92c983c5c7", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "notes": "This node merges the tags which are fetched via the form with the existing tags we requested in order to identify if new tags need to be created.", "position": [1700, 340], "parameters": {"mode": "combineBySql", "query": "SELECT \n    input1.tags AS name,  -- Extracts the tag name from input1\n    IF(input2.value IS NOT NULL, true, false) AS exist, -- Checks if the tag exists in input2 (returns true if found, false otherwise)\n    input2.id AS tag_id  -- Retrieves the ID of the tag from input2 if it exists, otherwise returns NULL\nFROM \n    input1\nLEFT JOIN \n    input2 \nON \n    input1.tags = input2.value  -- Matches tags from input1 with values in input2"}, "notesInFlow": true, "typeVersion": 3}, {"id": "3f643d7b-7acd-46ad-a31a-aa1cd4ec0424", "name": "Define Array of tags from Gravityforms", "type": "n8n-nodes-base.set", "notes": "This node defines tags based on the form submission, such as the webinar selection, date, and reminder interval, and saves them as an array for further processing.", "position": [1280, 240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "814576c1-ba16-4546-9815-2b7dec324f94", "name": "tags", "type": "array", "value": "={{ \n  Array.from([\n    // Extracts value from Typeform response (field 8), or returns null if not found\n    $('New submission via Gravityforms')?.item?.json?.body?.['8'] || null, \n    $('New submission via Gravityforms').item.json.body['13'],\n    (() => {\n      try {\n        // Extracts and parses JSON from Typeform response (field 11), or returns null if not found\n        let value = $('New submission via Gravityforms')?.item?.json?.body?.['11'];\n        return value ? JSON.parse(value) : null;\n      } catch (error) {\n        return null; // Return null if JSON parsing fails\n      }\n    })()\n  ].flat().filter(item => item !== null)) // Flattens the array and removes null values\n}}"}]}}, "notesInFlow": true, "typeVersion": 3.4}, {"id": "e52482ea-5604-4c4d-a202-de770d4fb240", "name": "Split Out Gravityforms tags", "type": "n8n-nodes-base.splitOut", "notes": "In this node we split the created array again into items so we can merge them with the existing tags we request from KlickTipp.", "position": [1460, 240], "parameters": {"options": {}, "fieldToSplitOut": "tags"}, "notesInFlow": true, "typeVersion": 1}, {"id": "3d020c2b-69d7-4c09-9b09-47ac4d87861c", "name": "New submission via Gravityforms", "type": "n8n-nodes-base.webhook", "notes": "This webhook node captures incoming data from the Gravity Forms plugin on the website. It triggers the workflow when a new form submission is received.", "position": [460, 340], "webhookId": "9e8feb6b-df09-4f17-baf0-9fa3b8c0093c", "parameters": {"path": "9e8feb6b-df09-4f17-baf0-9fa3b8c0093c", "options": {}, "httpMethod": "POST"}, "notesInFlow": true, "typeVersion": 2}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Tag creation check", "type": "main", "index": 0}]]}, "Tag creation check": {"main": [[{"node": "Aggregate tags to add to contact", "type": "main", "index": 0}], [{"node": "Create the tag in KlickTipp", "type": "main", "index": 0}]]}, "Create the tag in KlickTipp": {"main": [[{"node": "Aggregate array of created tags", "type": "main", "index": 0}]]}, "Split Out Gravityforms tags": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Convert and set feedback data": {"main": [[{"node": "Subscribe contact in KlickTipp", "type": "main", "index": 0}]]}, "Get list of all existing tags": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Subscribe contact in KlickTipp": {"main": [[{"node": "Define Array of tags from Gravityforms", "type": "main", "index": 0}, {"node": "Get list of all existing tags", "type": "main", "index": 0}]]}, "Aggregate array of created tags": {"main": [[{"node": "Tag contact KlickTipp after trag creation", "type": "main", "index": 0}]]}, "New submission via Gravityforms": {"main": [[{"node": "Convert and set feedback data", "type": "main", "index": 0}]]}, "Aggregate tags to add to contact": {"main": [[{"node": "Tag contact directly in KlickTipp", "type": "main", "index": 0}]]}, "Define Array of tags from Gravityforms": {"main": [[{"node": "Split Out Gravityforms tags", "type": "main", "index": 0}]]}}}