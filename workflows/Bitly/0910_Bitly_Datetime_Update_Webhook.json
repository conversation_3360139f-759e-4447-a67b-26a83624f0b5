{"meta": {"instanceId": "a144404b9eef9f0b32d0c43312a7a31a5b8a0e1f3be155816313521251b36cbc"}, "nodes": [{"id": "3feeda27-6a9a-4a87-aca4-62dc7a1009dc", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1180, 340], "parameters": {"options": {}}, "typeVersion": 1.7}, {"id": "b9d70ef3-a74a-4588-9d16-95a51995644a", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-780, 580], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": ""}, "options": {}, "messages": {"values": [{}]}}, "credentials": {"openAiApi": {"id": "cBDoeZ81f4rgsEu7", "name": "OpenAI"}}, "typeVersion": 1.8}, {"id": "e633062d-eae5-4d67-a1b8-d3df5fc87cb3", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-780, 340], "parameters": {}, "typeVersion": 1.5}, {"id": "4b69fa18-4018-4b1c-a4e3-43699c807fca", "name": "Information Extractor", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [-1180, 580], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "c2aaf017-07d2-452c-ba18-cc747e3979d0", "name": "Question and Answer Chain", "type": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "position": [-380, 580], "parameters": {"options": {}}, "typeVersion": 1.4}, {"id": "274da45c-d0bd-48d7-9263-78afa308c78b", "name": "Sentiment Analysis", "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "position": [-1180, 800], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "03be1fae-4ba5-45a3-a19c-2bb3ee49c6e9", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [-780, 800], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "f7cadacb-66bd-4fc7-8a5e-06c7fa8493cd", "name": "Text Classifier", "type": "@n8n/n8n-nodes-langchain.textClassifier", "position": [-380, 800], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "835ac56c-1c33-4955-b097-59c15dc58731", "name": "Chat Memory Manager", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [-380, 340], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "b03c385e-b4b3-430b-a8d1-629ccc785726", "name": "Bitly App", "type": "n8n-nodes-base.bitly", "position": [3837, -1200], "parameters": {"additionalFields": {}}, "typeVersion": 1}, {"id": "73276937-34bc-4943-87c3-1a6d57edac79", "name": "Dropbox App", "type": "n8n-nodes-base.dropbox", "position": [4277, -1200], "parameters": {"operation": "download", "authentication": "oAuth2"}, "credentials": {"dropboxOAuth2Api": {"id": "MS0Kqim8JzWcoeQT", "name": "Dropbox"}}, "typeVersion": 1}, {"id": "6a1d1bd1-8c91-4bf2-acbf-c3fac836a3bd", "name": "Gmail App", "type": "n8n-nodes-base.gmail", "position": [3837, -980], "webhookId": "221627ad-9bc1-4919-b105-6816f8cba493", "parameters": {"options": {}}, "typeVersion": 2.1}, {"id": "e0f92ee0-55b1-468f-9213-b8de41fb7e1c", "name": "Google Calendar App", "type": "n8n-nodes-base.googleCalendar", "position": [4277, -980], "parameters": {"calendar": {"__rl": true, "mode": "list", "value": ""}, "additionalFields": {}}, "credentials": {"googleCalendarOAuth2Api": {"id": "2RZbxwB5fmEcf6c8", "name": "Google Calendar - IversusAI"}}, "typeVersion": 1.3}, {"id": "abf825e0-8401-4e3b-ba3d-471e609706aa", "name": "Google Docs App", "type": "n8n-nodes-base.googleDocs", "position": [4497, -980], "parameters": {"operation": "get"}, "typeVersion": 2}, {"id": "e1f1c422-e20c-43b1-8dc9-41fb960a15a5", "name": "Google Sheets App", "type": "n8n-nodes-base.googleSheets", "position": [3837, -760], "parameters": {"operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": ""}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "credentials": {"googleSheetsOAuth2Api": {"id": "dfwpyu0cnqopqMt7", "name": "Google Sheets - IversusAI"}}, "typeVersion": 4.5}, {"id": "bbe5c37f-4c62-43fd-8348-f81ad5b6acbe", "name": "Pushbullet App", "type": "n8n-nodes-base.pushbullet", "position": [4497, -760], "parameters": {}, "typeVersion": 1}, {"id": "d9b3cea5-56c2-4441-b86c-0235ef1797d4", "name": "YouTube App", "type": "n8n-nodes-base.youTube", "position": [4500, -540], "parameters": {"options": {}, "resource": "playlist", "operation": "create"}, "credentials": {}, "typeVersion": 1}, {"id": "4e551552-e81d-4e68-9dcb-129457c7eeee", "name": "<PERSON><PERSON>", "type": "@muench-dev/n8n-nodes-bluesky.bluesky", "position": [4057, -1200], "parameters": {}, "typeVersion": 2}, {"id": "c4072976-9564-455f-bd5a-d6a90247b967", "name": "Perplexity App", "type": "@watzon/n8n-nodes-perplexity.perplexity", "position": [4277, -760], "parameters": {"additionalFields": {}}, "typeVersion": 1}, {"id": "371967ab-a033-401d-a193-4b1d16832cf1", "name": "ElevenLabs App", "type": "n8n-nodes-elevenlabs.elevenLabs", "position": [4497, -1200], "parameters": {"voice_id": {"__rl": true, "mode": "list", "value": null}, "requestOptions": {}, "additionalFields": {}}, "credentials": {"elevenLabsApi": {"id": "fYYTiIvdQpMpZJgy", "name": "ElevenLabs"}}, "typeVersion": 1}, {"id": "e058f764-64b2-4253-a097-e8da851ce709", "name": "Reddit App", "type": "n8n-nodes-base.reddit", "position": [3837, -540], "parameters": {}, "typeVersion": 1}, {"id": "623e73b1-6b53-43c4-b95c-ddc0dc67addd", "name": "G<PERSON> App", "type": "n8n-nodes-base.gmailTrigger", "position": [4057, -980], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1.2}, {"id": "8cbfb5b3-3b42-4a57-8878-e6a13881f940", "name": "Google Sheets Trigger App", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [4057, -760], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": ""}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 1}, {"id": "cea827e6-c499-494c-b8de-d85f7c6c520d", "name": "# Green", "type": "n8n-nodes-base.stickyNote", "position": [3620, -1380], "parameters": {"color": 4, "width": 1174, "height": 1074, "content": "# APP ACTIONS\n\n#### This section contains nodes for interacting with external apps and services like Google Sheets, Telegram, or Notion."}, "typeVersion": 1}, {"id": "151719d4-037d-4b20-acb6-c4c674337bc1", "name": "# Gray", "type": "n8n-nodes-base.stickyNote", "position": [380, 80], "parameters": {"color": 7, "width": 1174, "height": 1074, "content": "# AI TOOLS\n\n#### This section contains tools for use with AI Agents."}, "typeVersion": 1}, {"id": "8a61e929-aeaa-4af5-9a5a-2f69135c486a", "name": "Call n8n Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [800, 340], "parameters": {}, "typeVersion": 2}, {"id": "3c4cfce6-e995-4a72-be43-aba8d751ba27", "name": "Code Tool", "type": "@n8n/n8n-nodes-langchain.toolCode", "position": [1020, 340], "parameters": {}, "typeVersion": 1.1}, {"id": "b3c90dbc-f92a-4bbe-ae2c-837ad7fa5196", "name": "HTTP Request1", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1240, 540], "parameters": {}, "typeVersion": 1.1}, {"id": "2f382add-25cc-429d-9e7b-b84a4c632150", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [600, 340], "parameters": {}, "typeVersion": 1}, {"id": "583f6982-d6b3-4501-8341-da2b324f11f1", "name": "Postgres", "type": "n8n-nodes-base.postgresTool", "position": [800, 740], "parameters": {"table": {"__rl": true, "mode": "list", "value": ""}, "schema": {"__rl": true, "mode": "list", "value": "public"}}, "typeVersion": 2.5}, {"id": "bd3e5ccf-f21e-425f-b863-4f4b86a550db", "name": "Redis", "type": "n8n-nodes-base.redisTool", "position": [1020, 740], "parameters": {}, "typeVersion": 1}, {"id": "f26f5ed9-c782-4aca-affe-3cbf9eac4ae9", "name": "Send Email", "type": "n8n-nodes-base.emailSendTool", "position": [1240, 740], "webhookId": "aad16d99-06c6-4ff4-98dc-87e11eac6d10", "parameters": {"options": {}}, "typeVersion": 2.1}, {"id": "84f85cc4-6218-4c1b-b0d7-1103deeaa308", "name": "SerpAPI", "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "position": [600, 940], "parameters": {"options": {}}, "credentials": {"serpApi": {"id": "xonLZy0JUzY1IQ5E", "name": "SerpAPI"}}, "typeVersion": 1}, {"id": "17ae5580-caff-43c4-93f5-c7c48a31ac30", "name": "Wikipedia", "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "position": [800, 940], "parameters": {}, "typeVersion": 1}, {"id": "5d9454dc-fcd4-4b72-830f-e54af6a2680f", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.toolWolframAlpha", "position": [1020, 940], "parameters": {}, "typeVersion": 1}, {"id": "90bb8301-75fa-477a-8cd1-11d8cb3398e1", "name": "gmailTool App", "type": "n8n-nodes-base.gmailTool", "position": [1240, 340], "webhookId": "287836d6-287e-4488-92a1-b0719a512008", "parameters": {"options": {}}, "typeVersion": 2.1}, {"id": "cc712ce4-bb83-4635-8701-27dc5274b2bf", "name": "googleCalendarTool App", "type": "n8n-nodes-base.googleCalendarTool", "position": [600, 540], "parameters": {"calendar": {"__rl": true, "mode": "list", "value": ""}, "additionalFields": {}}, "credentials": {"googleCalendarOAuth2Api": {"id": "2RZbxwB5fmEcf6c8", "name": "Google Calendar - IversusAI"}}, "typeVersion": 1.3}, {"id": "92738fd9-c1ac-4dd2-a963-ced81df249cd", "name": "googleDocsTool App", "type": "n8n-nodes-base.googleDocsTool", "position": [800, 540], "parameters": {}, "typeVersion": 2}, {"id": "4f82cf51-aebe-4914-b1b0-b661a40533a9", "name": "googleSheetsTool App", "type": "n8n-nodes-base.googleSheetsTool", "position": [1020, 540], "parameters": {"sheetName": {"__rl": true, "mode": "list", "value": ""}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "credentials": {"googleSheetsOAuth2Api": {"id": "dfwpyu0cnqopqMt7", "name": "Google Sheets - IversusAI"}}, "typeVersion": 4.5}, {"id": "ce6e07ea-4081-4832-ab5c-58d23638851c", "name": "# Purple", "type": "n8n-nodes-base.stickyNote", "position": [2020, 80], "parameters": {"color": 6, "width": 1174, "height": 1074, "content": "# VECTOR MEMORY\n\n#### This section contains tools for AI Agents related to memory storage."}, "typeVersion": 1}, {"id": "0c82bae6-54af-45d9-b530-f865460193ce", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [2840, 780], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "f3694575-a9cd-4800-b830-71c9505bcb19", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [2500, 780], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "bb6da0de-472c-43f5-9b93-c7211493ffbf", "name": "Answer questions with a vector store", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [2160, 780], "parameters": {}, "typeVersion": 1}, {"id": "ac10ddbc-81d2-470e-8a19-740da3bbeed8", "name": "In-Memory Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [2300, 360], "parameters": {"mode": "retrieve-as-tool"}, "typeVersion": 1}, {"id": "382cad17-dfc9-457f-9e68-27eb687ec233", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [2680, 360], "parameters": {"mode": "retrieve-as-tool", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 1}, {"id": "1f4eee34-ebc5-4899-acb9-a140863eca88", "name": "Postgres PGVector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "position": [2300, 560], "parameters": {"mode": "retrieve-as-tool", "options": {}}, "typeVersion": 1}, {"id": "44806f55-b717-4885-b750-37e1655dfc47", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "position": [2680, 560], "parameters": {"mode": "retrieve-as-tool", "options": {}, "tableName": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 1}, {"id": "d5037d83-ea19-42ab-86e7-272c69a47342", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [3880, 340], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "b148f82d-8343-4856-aab5-2811ee99a8c4", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [4140, 340], "parameters": {"options": {}}, "credentials": {}, "typeVersion": 1}, {"id": "27f45f90-7197-4e35-aedd-c158a7af55a7", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4420, 340], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cBDoeZ81f4rgsEu7", "name": "OpenAI"}}, "typeVersion": 1.1}, {"id": "79af5566-476d-45df-9992-5d63050291f3", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [4420, 700], "parameters": {}, "typeVersion": 1.3}, {"id": "b6182791-6d01-40e9-9510-333dc6307cca", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "position": [3880, 700], "parameters": {}, "typeVersion": 1.3}, {"id": "d0047650-53e9-4fe8-849d-83324abca9d3", "name": "Redis <PERSON>", "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "position": [4140, 700], "parameters": {}, "typeVersion": 1.4}, {"id": "bd5fd081-2c33-4a9f-9b75-6b26304d3f90", "name": "Item List Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserItemList", "position": [4000, 880], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "7f0087cb-9791-4339-baf9-5263f18a25ad", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [4300, 880], "parameters": {}, "typeVersion": 1.2}, {"id": "a6d3936b-ad3c-4864-bdf7-a3369d0f0ce2", "name": "Embeddings Google Gemini", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [4000, 520], "parameters": {}, "credentials": {}, "typeVersion": 1}, {"id": "6439f8d0-86e3-449a-ac90-c0e057080975", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [4300, 520], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "cBDoeZ81f4rgsEu7", "name": "OpenAI"}}, "typeVersion": 1.2}, {"id": "3ff47eb5-59b8-4f69-ba91-f07584f2fbc5", "name": "# Blue", "type": "n8n-nodes-base.stickyNote", "position": [3620, 80], "parameters": {"color": 5, "width": 1174, "height": 1074, "content": "# MISCELLANEOUS AI TOOLS\n\n#### This section contains miscellaneous tools for AI Agents including models, embeddings, memory and parsers."}, "typeVersion": 1}, {"id": "ee5c44f7-a56d-4eb0-9f04-f7e35c56aa12", "name": "Sticky Note Red", "type": "n8n-nodes-base.stickyNote", "position": [2040, 1440], "parameters": {"color": 3, "content": "# Red"}, "typeVersion": 1}, {"id": "83858128-51a4-4947-8e1f-1f0d8b002a94", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1300, 1720], "parameters": {"color": 4, "content": "# Green"}, "typeVersion": 1}, {"id": "cc1311e0-b005-4460-b841-f1e5961abd80", "name": "Sticky Note Blue", "type": "n8n-nodes-base.stickyNote", "position": [1660, 1720], "parameters": {"color": 5, "content": "# Blue"}, "typeVersion": 1}, {"id": "fc0b7463-305c-47dd-9d30-da4f3e8a1708", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2040, 1720], "parameters": {"color": 6, "content": "# Purple"}, "typeVersion": 1}, {"id": "0e7fed27-5c8b-4655-9c27-84ce94db7f5e", "name": "<PERSON><PERSON> <PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1660, 1980], "parameters": {"color": 7, "content": "# Gray"}, "typeVersion": 1}, {"id": "a8992b45-7788-43d6-8060-1b115efe4879", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1660, 1440], "parameters": {"color": 2, "content": "# Brown"}, "typeVersion": 1}, {"id": "b866c03d-d46c-4832-b92f-0ea86599b81f", "name": "<PERSON>y <PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1300, 1440], "parameters": {"content": "# Yellow"}, "typeVersion": 1}, {"id": "2df946c3-336f-486c-8386-8ee7075f77a4", "name": "X", "type": "n8n-nodes-base.twitter", "position": [4280, -540], "parameters": {"additionalFields": {}}, "typeVersion": 2}, {"id": "6b4bb993-28d0-4d37-9947-ed217f3d528f", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.calendlyTrigger", "position": [-1080, -1200], "webhookId": "0fcbe862-fa72-496c-8504-fe2fd903a70a", "parameters": {"events": ["invitee.created", "invitee.canceled"]}, "typeVersion": 1}, {"id": "d8eaab32-12bd-45c4-ac00-300fdd898de6", "name": "<PERSON><PERSON> (IMAP)", "type": "n8n-nodes-base.emailReadImap", "position": [-860, -1200], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "808bd54d-3bd1-4cb5-9317-96293e809323", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "position": [-420, -1200], "parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "mode": "list", "value": ""}}, "credentials": {"googleDriveOAuth2Api": {"id": "XlZKNfvws03EtUPV", "name": "Google Drive - IversusAI"}}, "typeVersion": 1}, {"id": "0e152939-2353-4d13-85f9-9096785562a8", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.gumroadTrigger", "position": [-860, -980], "webhookId": "0d650798-d9d7-439e-a266-b6205233afea", "parameters": {}, "typeVersion": 1}, {"id": "c9ab2220-b03b-4788-a68e-f6b03f2e3098", "name": "Local File Trigger", "type": "n8n-nodes-base.localFileTrigger", "position": [-640, -980], "parameters": {"options": {}, "triggerOn": "folder"}, "typeVersion": 1}, {"id": "60144d8d-2e0f-49d9-ba7d-05b2363ddf74", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-420, -980], "webhookId": "6b73f639-28a8-4282-8452-a8374036b263", "parameters": {"options": {}, "formFields": {"values": [{}]}}, "typeVersion": 2.2}, {"id": "fb224b15-5e25-4274-8bdd-ee1e1785d9f1", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1080, -760], "parameters": {"rule": {"interval": [{}]}}, "typeVersion": 1.2}, {"id": "cdaac578-99db-46ae-8987-4ad35cfbc166", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-860, -760], "webhookId": "5d58aa36-a90f-4ec3-ab44-2006a370ae56", "parameters": {"path": "5d58aa36-a90f-4ec3-ab44-2006a370ae56", "options": {}}, "typeVersion": 2}, {"id": "4c82af95-1614-46a2-8f44-1a32a1ce6087", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-640, -760], "webhookId": "58bd0a92-e352-402c-9070-0278ea9cb0ac", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "10e7394d-3bec-4cb7-8b86-d5f9c5942881", "name": "When clicking 'Test workflow'", "type": "n8n-nodes-base.manualTrigger", "position": [-420, -760], "parameters": {}, "typeVersion": 1}, {"id": "0d207099-c44e-4393-a02b-b4f97c8610cd", "name": "Workflow Input Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-1080, -540], "parameters": {}, "typeVersion": 1.1}, {"id": "2c0910cc-dc9b-4c38-acd6-05315f373c3f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-640, -1200], "parameters": {"filters": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1.2}, {"id": "909d7d34-8d08-4452-b8ab-641d1566b7d7", "name": "Google Sheets Trigger", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [-1080, -980], "parameters": {"options": {}, "pollTimes": {"item": [{"mode": "everyMinute"}]}, "sheetName": {"__rl": true, "mode": "list", "value": ""}, "documentId": {"__rl": true, "mode": "list", "value": ""}}, "typeVersion": 1}, {"id": "fc7fe9cf-1b62-4b96-9a19-72b8510ad8e6", "name": "# Purple1", "type": "n8n-nodes-base.stickyNote", "position": [-1300, -1380], "parameters": {"color": 6, "width": 1274, "height": 1074, "content": "# TRIGGERS\n\n#### This section contains all trigger nodes that can start workflow execution."}, "typeVersion": 1}, {"id": "912f97da-0920-46a5-b37b-d5dc6cdfc4c4", "name": "Code", "type": "n8n-nodes-base.code", "position": [780, -1200], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "7b59ae12-f26a-4571-bad1-6e253a4456b0", "name": "Date & Time", "type": "n8n-nodes-base.dateTime", "position": [1220, -1200], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "e9110354-6b3f-40d4-a42c-2b5fa19cf9f0", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [560, -980], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "f5f4fd27-b7e2-49df-9ea3-9998119c07d3", "name": "Name Test", "type": "string", "value": "Value Test"}, {"id": "d9658a2c-93a5-48c1-a170-9f8182d5a113", "name": "Name 1", "type": "number", "value": 1}]}}, "typeVersion": 3.4}, {"id": "458376a1-b116-4c4f-a01b-8fb5fd5337ad", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [1000, -980], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "6a4cc4e4-a4a3-4d9e-a19f-c1189749e356", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "338520d1-b0f7-4463-88f2-59a9129cf3ad", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [560, -760], "parameters": {}, "typeVersion": 1}, {"id": "e86e17ef-d46d-487a-aeaa-a62a57c28cea", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [1220, -760], "parameters": {"options": {}}, "typeVersion": 2}, {"id": "ed62f9ee-126b-40f4-b090-37837b9aae90", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [1000, -540], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "9439328a-b787-49fe-829b-35efe6f6f94d", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [560, -1200], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{}]}}, "typeVersion": 1}, {"id": "6ab55c99-bbe7-4608-b686-56e10cb03209", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1000, -760], "parameters": {}, "typeVersion": 3}, {"id": "59bba8e3-1867-4fe4-93b7-be9884a99eeb", "name": "Summarize", "type": "n8n-nodes-base.summarize", "position": [1220, -540], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{}]}}, "typeVersion": 1}, {"id": "1e0747d4-e2ca-4592-ac28-73651dd5973b", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "position": [1000, -1200], "parameters": {"options": {}, "operation": "toText"}, "typeVersion": 1.1}, {"id": "586bcd83-db67-4d92-84ce-70f8126b3ef8", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [780, -980], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "e51edfa8-7d70-4ee8-b6dc-a4f0f4c2d0ce", "name": "HTML", "type": "n8n-nodes-base.html", "position": [1220, -980], "parameters": {"options": {}, "operation": "extractHtmlContent", "extractionValues": {"values": [{}]}}, "typeVersion": 1.2}, {"id": "9c5a2f49-452b-4e73-b1dc-c4b043d87ec1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [780, -760], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "6269bc98-cc16-4608-8f63-125f348f25e6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.rename<PERSON><PERSON>s", "position": [560, -540], "parameters": {"additionalOptions": {}}, "typeVersion": 1}, {"id": "a25145c1-a269-43fc-8d51-03d45d340cb3", "name": "Sort", "type": "n8n-nodes-base.sort", "position": [780, -540], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "398302d7-97f8-4d88-a9e1-1d59f1e0522d", "name": "# Blue1", "type": "n8n-nodes-base.stickyNote", "position": [380, -1380], "parameters": {"color": 5, "width": 1174, "height": 1074, "content": "# DATA TRANSFORMATION\n\n#### This section contains nodes for manipulating, filtering, and converting data."}, "typeVersion": 1}, {"id": "6b2896d4-308c-4dc6-8851-275a791a5957", "name": "If", "type": "n8n-nodes-base.if", "position": [2440, -980], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f9de618d-6acc-46a9-a7ec-61c748ce8c31", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "83eecf41-341e-428e-be4b-907d610214ea", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [2660, -980], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "e2623f85-fbbf-4c3e-b8a1-7393b5c10b10", "name": "Replace Me", "type": "n8n-nodes-base.noOp", "notes": "Placeholder node - replace with actual processing node", "position": [2880, -980], "parameters": {}, "typeVersion": 1}, {"id": "9a347885-e8b6-47c0-bcc6-9b5a9de31630", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [2440, -1200], "parameters": {"options": {}}, "typeVersion": 1.2}, {"id": "535e56d1-dd6d-48b5-a83a-4237b98e6712", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [2440, -760], "webhookId": "34bf5501-158c-43fb-b571-067c2dfaa5fc", "parameters": {"resume": "webhook", "options": {}}, "typeVersion": 1.1}, {"id": "24eb6d34-7812-4c25-9e52-b06a47e1d8fc", "name": "Execute Command", "type": "n8n-nodes-base.executeCommand", "position": [2220, -1200], "parameters": {}, "typeVersion": 1}, {"id": "fb3342d9-1a40-417a-bc30-641bdc858454", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [2220, -980], "parameters": {"options": {}}, "typeVersion": 4.2}, {"id": "dd2c5db8-ad89-4e8b-a2ca-b2937d5bb504", "name": "Execution Data", "type": "n8n-nodes-base.executionData", "position": [2660, -1200], "parameters": {}, "typeVersion": 1}, {"id": "ed45b513-7fe5-4620-b190-8022ab3b0c27", "name": "FTP", "type": "n8n-nodes-base.ftp", "position": [2880, -1200], "parameters": {"protocol": "sftp"}, "typeVersion": 1}, {"id": "f8d75e51-e391-4ee6-aeda-d4ee1b95535c", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2220, -760], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "b31e8cf2-ef95-4183-904a-e426460b6cf0", "name": "# Red", "type": "n8n-nodes-base.stickyNote", "position": [2020, -1380], "parameters": {"color": 3, "width": 1174, "height": 1074, "content": "# FLOW & CORE\n\n#### This section contains flow control nodes (branch, merge, or loop workflows) and core functionality nodes (run code, make HTTP requests, accept webhooks)."}, "typeVersion": 1}, {"id": "772785e7-e4e8-46bb-881a-f46535001357", "name": "RSS Read", "type": "n8n-nodes-base.rssFeedRead", "position": [4060, -540], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "2bacc070-b61f-413c-9a04-645165e4737e", "name": "# Red1", "type": "n8n-nodes-base.stickyNote", "position": [-1280, 80], "parameters": {"color": 3, "width": 1274, "height": 1074, "content": "# AI AGENTS\n\n#### This section contains all AI related nodes that can attach models, tools and memory."}, "typeVersion": 1}, {"id": "c2b59df8-6327-464d-bb98-29c6df9e521a", "name": "AI Transform", "type": "n8n-nodes-base.aiTransform", "position": [-700, 980], "parameters": {}, "typeVersion": 1}, {"id": "e9e27f3b-6f85-40d6-ba08-d14e75ee9ebc", "name": "MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [600, 740], "parameters": {}, "typeVersion": 1}, {"id": "9a7807d3-b789-4067-b549-eed5e297e524", "name": "Sticky Note Purple1", "type": "n8n-nodes-base.stickyNote", "position": [-2720, -1380], "parameters": {"color": 7, "width": 1020, "height": 1080, "content": "# WATCH THE n8n STARTER GUIDE 👇\n\n[![Click Here!](https://i.imgur.com/GpUl9iS.png)](https://www.youtube.com/watch?v=It3CkokmodE&list=PL1Ylp5hLJfWeL9ZJ0MQ2sK5y2wPYKfZdE&index=1&pp=gAQBiAQBsAQB)\n\n\n## THE NODE REFERENCE LIBRARY 📖\n\n## This **Node Reference Library** workflow is like a visual map showing many common n8n nodes, grouped by what they do (like Triggers, Data Transformation, AI Agents, etc.). Think of it as a quick visual cheat sheet! 🗺️\n\n## Explore the canvas to get familiar with different node types and see what's possible. ✨\n\n## This resource is provided by [@IversusAI](https://www.youtube.com/@IversusAI) on YouTube! 📺\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"OpenAI": {"main": [[]]}, "AI Agent": {"main": [[]]}, "Replace Me": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[]]}, "Basic LLM Chain": {"main": [[]]}, "Loop Over Items": {"main": [[], [{"node": "Replace Me", "type": "main", "index": 0}]]}, "Sentiment Analysis": {"main": [[]]}, "Summarization Chain": {"main": [[]]}, "Information Extractor": {"main": [[]]}, "Question and Answer Chain": {"main": [[]]}}}