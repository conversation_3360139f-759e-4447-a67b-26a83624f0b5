{"id": "208", "name": "Add a datapoint to <PERSON><PERSON><PERSON> when new activity is added to Strava", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.stravaTrigger", "position": [470, 300], "webhookId": "2b0c6812-ac24-42e5-b15e-8d1fb7606908", "parameters": {"event": "create", "options": {}}, "credentials": {"stravaOAuth2Api": "strava"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.beeminder", "position": [670, 300], "parameters": {"goalName": "testing", "additionalFields": {"comment": "={{$json[\"object_data\"][\"name\"]}}"}}, "credentials": {"beeminderApi": "Beeminder credentials"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Strava Trigger": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}